{"manifest_version": 3, "name": "MarkDownload - Markdown Web Clipper", "version": "3.3.0", "author": "<PERSON>", "description": "This extension works like a web clipper, but it downloads articles in markdown format.", "icons": {"16": "icons/favicon-16x16.png", "32": "icons/favicon-32x32.png", "48": "icons/favicon-48x48.png", "128": "icons/appicon-128x128.png", "192": "icons/favicon-192x192.png", "512": "icons/favicon-512x512.png"}, "permissions": ["activeTab", "downloads", "storage", "contextMenus", "clipboardWrite", "scripting"], "host_permissions": ["<all_urls>"], "action": {"default_title": "MarkDownload", "default_popup": "popup/popup.html", "default_icon": {"16": "icons/favicon-16x16.png", "32": "icons/favicon-32x32.png", "48": "icons/favicon-48x48.png", "128": "icons/appicon-128x128.png"}}, "background": {"service_worker": "background/service-worker.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["browser-polyfill.min.js", "contentScript/contentScript.js", "contentScript/pageContext.js"], "run_at": "document_end"}], "options_ui": {"page": "options/options.html", "open_in_tab": false}, "commands": {"_execute_browser_action": {"suggested_key": {"default": "Alt+Shift+M"}}, "download_tab_as_markdown": {"suggested_key": {"default": "Alt+Shift+D"}, "description": "Save current tab as <PERSON><PERSON>"}, "copy_tab_as_markdown": {"suggested_key": {"default": "Alt+Shift+C"}, "description": "Copy current tab as Mark<PERSON> to the clipboard"}, "copy_selection_as_markdown": {"description": "Copy current selection as Mark<PERSON> to the clipboard"}, "copy_tab_as_markdown_link": {"suggested_key": {"default": "Alt+Shift+L"}, "description": "Copy current tab URL as Markdown link to the clipboard"}, "copy_selected_tab_as_markdown_link": {"description": "<PERSON><PERSON> selected tabs URL as Markdown link to the clipboard"}, "copy_selection_to_obsidian": {"description": "<PERSON><PERSON> current selection as <PERSON><PERSON> to Obsidian"}, "copy_tab_to_obsidian": {"description": "Copy current tab as Mark<PERSON> to Obsidian"}}, "browser_specific_settings": {"gecko": {"id": "{1c5e4c6f-5530-49a3-b216-31ce7d744db0}", "strict_min_version": "65.0"}}}