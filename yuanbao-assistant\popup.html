<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>元宝助手</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="text-logo">元</div>
      <h3>元宝助手<span class="version">1.0</span></h3>
      <div class="header-actions">
        <button id="historyBtn" class="icon-btn" title="查看历史记录"><span class="history-icon"></span></button>
        <a href="options.html" target="_blank" class="settings-link">⚙️</a>
      </div>
    </div>
    
    <div id="status" class="status waiting">等待元宝回答...</div>
    
    <!-- 手动检测按钮 -->
    <button id="checkBtn" class="check-btn">手动检测</button>
    
    <!-- 模式选择开关 -->
    <div class="mode-selector">
      <span>提取模式:</span>
      <div class="toggle-container">
        <span class="mode-label">思考部分</span>
        <label class="toggle-switch">
          <input type="checkbox" id="modeToggle" checked>
          <span class="slider"></span>
        </label>
        <span class="mode-label">完整回答</span>
      </div>
    </div>
    
    <!-- API类型选择 -->
    <div class="api-type-selector">
      <label for="apiTypeSelect">API类型:</label>
      <select id="apiTypeSelect">
        <option value="openrouter" selected>OpenRouter</option>
        <option value="native">Gemini原生</option>
      </select>
    </div>
    
    <!-- OpenRouter模型选择 (仅在OpenRouter API类型下显示) -->
    <div id="openrouterModelContainer" class="model-selector">
      <label for="modelSelect">使用模型:</label>
      <select id="modelSelect">
        <option value="google/gemini-2.5-flash-preview-05-20" selected>gemini-2.5-flash</option>
        <option value="google/gemini-2.5-flash-preview-05-20:thinking">gemini-2.5-flash（thinking)</option>
        <option value="google/gemini-2.0-flash-001">gemini-2.0-flash</option>
        <option value="anthropic/claude-3.7-sonnet">Claude 3.7 Sonnet</option>
        <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</option>
        <option value="anthropic/claude-sonnet-4">claude-sonnet-4</option>
        <option value="custom">自定义模型</option>
      </select>
    </div>
    
    <!-- 原生Gemini模型选择 (仅在原生API类型下显示) -->
    <div id="nativeModelContainer" class="model-selector" style="display: none;">
      <label for="nativeModelSelect">Gemini模型:</label>
      <select id="nativeModelSelect">
        <option value="gemini-2.5-pro-exp-03-25" selected>Gemini 2.5 Pro</option>
        <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
        <option value="gemini-2.0-flash-thinking-exp-01-21">Gemini 2.0 Flash Thinking</option>
        <option value="gemini-2.5-flash-preview-05-20">gemini-2.5-flash</option>
      </select>
    </div>
    
    <!-- 自定义模型ID输入框，默认隐藏 -->
    <div id="customModelContainer" class="custom-model-container" style="display: none; margin-top: 8px;">
      <label for="customModelId">自定义模型ID:</label>
      <input type="text" id="customModelId" placeholder="输入完整的模型ID" style="width: 100%; padding: 6px; box-sizing: border-box;">
    </div>
    
    <!-- 提示词编辑区 -->
    <div class="prompt-section">
      <label for="prompt">提示词 (可编辑):</label>
      <textarea id="prompt">请根据提供内容逻辑框架回答，要更具体、更细化、更富有条例回答，注意不是单纯修改，可以改变文风，补充不足</textarea>
    </div>
    
    <!-- 操作按钮 -->
    <button id="submitBtn" class="primary-btn" disabled>提交给AI优化</button>
    
    <!-- API调用状态显示 -->
    <div id="apiStatusContainer" class="api-status-container" style="display: none;">
      <div class="api-status-header">
        <h4>API调用状态</h4>
        <button id="retryBtn" class="small-btn" style="display: none;">重试</button>
      </div>
      <div id="apiStatus" class="api-status">
        <div class="status-indicator"></div>
        <div class="status-message">
          <div class="loading-text">
            <span class="loading-message">正在准备...</span>
            <span class="loading-dots"></span>
          </div>
          <div class="loading-substatus"></div>
        </div>
        <div class="progress-indicator">
          <div class="progress-bar"></div>
        </div>
      </div>
      <div id="apiErrorDetails" class="api-error-details" style="display: none;"></div>
    </div>
    
    <!-- 结果显示区 -->
    <div id="resultContainer" style="display: none;">
      <div class="result-header">
        <h4>优化结果:</h4>
        <div class="actions">
          <button id="copyBtn" class="action-btn" title="复制到剪贴板"><span class="copy-icon"></span></button>
        </div>
      </div>
      <div id="result" class="result"></div>
    </div>
    
    <!-- 底部状态条 -->
    <div class="footer">
      <span id="statusMessage">准备就绪</span>
    </div>
    
    <!-- 历史记录面板 -->
    <div id="historyPanel" class="history-panel" style="display: none;">
      <div class="history-header">
        <h4>历史记录</h4>
        <div class="history-actions">
          <button id="exportHistoryFromPopupBtn" class="small-btn" title="导出历史记录">导出</button>
          <button id="closeHistoryBtn" class="icon-btn">✖</button>
        </div>
      </div>
      <div id="historyList" class="history-list">
        <div class="loading-history">加载中...</div>
      </div>
    </div>
  </div>
  <script src="popup.js"></script>
</body>
</html> 