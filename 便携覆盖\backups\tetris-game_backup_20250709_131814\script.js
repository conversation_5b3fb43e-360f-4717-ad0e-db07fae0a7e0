const gameBoard = document.getElementById('game-board');
const COLS = 10;
const ROWS = 20;
let board = Array.from({ length: ROWS }, () => Array(COLS).fill(0));

// Game state management
let gameState = 'playing'; // 'playing', 'paused', 'gameover'
let score = 0;
let level = 1;
let linesCleared = 0;
let highScore = localStorage.getItem('tetrisHighScore') || 0;
let blockElements = new Map(); // Cache DOM elements
let currentDifficulty = 'easy'; // 'easy' or 'hard'
let isGameStarted = false;

// Difficulty settings
const DIFFICULTY_SETTINGS = {
    easy: {
        name: '容易',
        initialSpeed: 800,
        speedDecrease: 60,
        minSpeed: 200,
        levelUpLines: 10,
        scoreMultiplier: 1
    },
    hard: {
        name: '困难',
        initialSpeed: 600,
        speedDecrease: 80,
        minSpeed: 100,
        levelUpLines: 8,
        scoreMultiplier: 1.5
    }
};

// Tetromino shapes and their colors
const SHAPES = [
    { shape: [[1, 1, 1, 1]], color: 1 }, // I
    { shape: [[1, 1], [1, 1]], color: 2 },   // O
    { shape: [[0, 1, 0], [1, 1, 1]], color: 3 }, // T
    { shape: [[0, 1, 1], [1, 1, 0]], color: 4 }, // S
    { shape: [[1, 1, 0], [0, 1, 1]], color: 5 }, // Z
    { shape: [[1, 0, 0], [1, 1, 1]], color: 6 }, // J
    { shape: [[0, 0, 1], [1, 1, 1]], color: 7 }  // L
];

let currentShape, currentColor, currentX, currentY;

// --- Game Loop and Timing Variables ---
let lastTimestamp = 0;
let dropInterval = DIFFICULTY_SETTINGS.easy.initialSpeed; // ms
let gameLoopId;

// --- Gamepad State Variables ---
let gamepadInputCooldown = 150; // ms between gamepad inputs
let lastGamepadInputTime = 0;

window.addEventListener("gamepadconnected", (e) => {
  console.log(
    "Gamepad connected at index %d: %s. %d buttons, %d axes.",
    e.gamepad.index,
    e.gamepad.id,
    e.gamepad.buttons.length,
    e.gamepad.axes.length,
  );
});

window.addEventListener("gamepaddisconnected", (e) => {
  console.log("Gamepad disconnected from index %d: %s", e.gamepad.index, e.gamepad.id);
});


function draw() {
    // Clear existing blocks
    gameBoard.innerHTML = '';
    blockElements.clear();
    
    // Create fragment for better performance
    const fragment = document.createDocumentFragment();
    
    // Draw static blocks
    board.forEach((row, y) => {
        row.forEach((cell, x) => {
            if (cell) {
                const block = createBlock(cell, x, y);
                fragment.appendChild(block);
                blockElements.set(`${x},${y}`, block);
            }
        });
    });

    // Draw current shape
    if (currentShape) {
        currentShape.shape.forEach((row, y) => {
            row.forEach((value, x) => {
                if (value) {
                    const block = createBlock(currentColor, currentX + x, currentY + y);
                    fragment.appendChild(block);
                }
            });
        });
    }
    
    gameBoard.appendChild(fragment);
}

function createBlock(colorIndex, x, y) {
    const block = document.createElement('div');
    block.className = 'block';
    block.classList.add(`color-${colorIndex}`);
    block.style.gridRowStart = y + 1;
    block.style.gridColumnStart = x + 1;
    return block;
}

function newShape() {
    const shapeIndex = Math.floor(Math.random() * SHAPES.length);
    const newPiece = SHAPES[shapeIndex];
    currentShape = newPiece;
    currentColor = newPiece.color;
    currentX = Math.floor((COLS - currentShape.shape[0].length) / 2);
    currentY = 0;

    if (isCollision()) {
        gameState = 'gameover';
        showGameOverModal();
        
        // Re-enable difficulty buttons
        document.getElementById('easy-btn').disabled = false;
        document.getElementById('hard-btn').disabled = false;
        document.getElementById('start-btn').textContent = '开始游戏';
        isGameStarted = false;
    }
}

function resetGame() {
    cancelAnimationFrame(gameLoopId);
    board = Array.from({ length: ROWS }, () => Array(COLS).fill(0));
    score = 0;
    level = 1;
    linesCleared = 0;
    dropInterval = DIFFICULTY_SETTINGS[currentDifficulty].initialSpeed;
    gameState = 'playing';
    isGameStarted = false;
    updateDisplay();
    draw(); // Clear the board display
}

function pauseGame() {
    if (gameState === 'playing') {
        gameState = 'paused';
        cancelAnimationFrame(gameLoopId);
    } else if (gameState === 'paused') {
        gameState = 'playing';
        lastTimestamp = performance.now();
        gameLoopId = requestAnimationFrame(gameLoop);
    }
}

// Difficulty selection functions
function setDifficulty(difficulty) {
    if (isGameStarted) return; // Don't allow changing difficulty during game
    
    currentDifficulty = difficulty;
    
    // Update button states
    document.getElementById('easy-btn').classList.remove('active');
    document.getElementById('hard-btn').classList.remove('active');
    document.getElementById(difficulty + '-btn').classList.add('active');
    
    // Reset game settings
    dropInterval = DIFFICULTY_SETTINGS[difficulty].initialSpeed;
    updateDisplay();
}

function startNewGame() {
    resetGame();
    isGameStarted = true;
    gameState = 'playing';
    
    // Disable difficulty buttons during game
    document.getElementById('easy-btn').disabled = true;
    document.getElementById('hard-btn').disabled = true;
    document.getElementById('start-btn').textContent = '重新开始';
    
    startGame();
}

function update() {
    currentY++;
    if (isCollision()) {
        currentY--;
        merge();
        clearLines();
        newShape();
    }
}

function merge() {
    currentShape.shape.forEach((row, y) => {
        row.forEach((value, x) => {
            if (value) {
                board[currentY + y][currentX + x] = currentColor;
            }
        });
    });
}

function clearLines() {
    let linesCleared = 0;
    for (let y = ROWS - 1; y >= 0; y--) {
        if (board[y].every(cell => cell !== 0)) {
            board.splice(y, 1);
            board.unshift(Array(COLS).fill(0));
            linesCleared++;
            y++;
        }
    }
    
    if (linesCleared > 0) {
        updateScore(linesCleared);
    }
}

function updateScore(lines) {
    const settings = DIFFICULTY_SETTINGS[currentDifficulty];
    const lineScores = [0, 40, 100, 300, 1200];
    score += Math.floor(lineScores[lines] * level * settings.scoreMultiplier);
    linesCleared += lines;
    
    // Level up based on difficulty
    if (Math.floor(linesCleared / settings.levelUpLines) >= level) {
        level++;
        // Calculate new speed based on difficulty
        const newSpeed = settings.initialSpeed - (level - 1) * settings.speedDecrease;
        dropInterval = Math.max(settings.minSpeed, newSpeed);
    }
    
    updateDisplay();
}

function updateDisplay() {
    // Update score display if elements exist
    const scoreElement = document.getElementById('score');
    const levelElement = document.getElementById('level');
    const difficultyElement = document.getElementById('difficulty');
    const highScoreElement = document.getElementById('high-score');
    
    if (scoreElement) scoreElement.textContent = score;
    if (levelElement) levelElement.textContent = level;
    if (difficultyElement) difficultyElement.textContent = DIFFICULTY_SETTINGS[currentDifficulty].name;
    if (highScoreElement) highScoreElement.textContent = highScore;
}

function isCollision() {
    for (let y = 0; y < currentShape.shape.length; y++) {
        for (let x = 0; x < currentShape.shape[y].length; x++) {
            if (currentShape.shape[y][x]) {
                const newX = currentX + x;
                const newY = currentY + y;
                if (newX < 0 || newX >= COLS || newY >= ROWS || (newY >= 0 && board[newY][newX] !== 0)) {
                    return true;
                }
            }
        }
    }
    return false;
}

function rotate() {
    const originalShape = JSON.parse(JSON.stringify(currentShape.shape));
    const newShape = [];
    for (let y = 0; y < originalShape[0].length; y++) {
        newShape[y] = [];
        for (let x = 0; x < originalShape.length; x++) {
            newShape[y][x] = originalShape[originalShape.length - 1 - x][y];
        }
    }
    
    const originalX = currentX;
    currentShape.shape = newShape;

    let offset = 1;
    while(isCollision()) {
        currentX += offset;
        offset = -(offset + (offset > 0 ? 1 : -1));
        if (Math.abs(offset) > currentShape.shape[0].length + 1) {
            currentShape.shape = originalShape;
            currentX = originalX;
            return false; // Indicate rotation failed
        }
    }
    return true; // Indicate rotation succeeded
}

// --- Input Handling ---

document.addEventListener('keydown', e => {
    if (gameState !== 'playing' && e.key !== ' ') return;
    
    let moved = false;
    if (e.key === 'ArrowLeft') {
        currentX--;
        if (isCollision()) currentX++; else moved = true;
    } else if (e.key === 'ArrowRight') {
        currentX++;
        if (isCollision()) currentX--; else moved = true;
    } else if (e.key === 'ArrowUp') {
        if(rotate()) moved = true;
    } else if (e.key === ' ') {
        pauseGame();
        return;
    }

    if (moved) {
        draw();
    }

    if (e.key === 'ArrowDown') {
        update();
        draw();
        lastTimestamp = performance.now();
    }
});

function handleGamepadInput(timestamp) {
    if (timestamp - lastGamepadInputTime < gamepadInputCooldown) return;

    const gamepads = navigator.getGamepads();
    if (!gamepads || !gamepads[0]) return;
    const gp = gamepads[0];

    let moved = false;

    // D-pad
    if (gp.buttons[14] && gp.buttons[14].pressed) { // D-pad Left
        currentX--;
        if (isCollision()) currentX++; else moved = true;
    } else if (gp.buttons[15] && gp.buttons[15].pressed) { // D-pad Right
        currentX++;
        if (isCollision()) currentX--; else moved = true;
    }

    // Analog stick
    if (gp.axes[0] < -0.5) { // Left stick left
        currentX--;
        if (isCollision()) currentX++; else moved = true;
    } else if (gp.axes[0] > 0.5) { // Left stick right
        currentX++;
        if (isCollision()) currentX--; else moved = true;
    }

    // Rotation (Button 0 is often 'A' on Xbox or 'X' on PlayStation)
    if (gp.buttons[0] && gp.buttons[0].pressed) {
        if(rotate()) moved = true;
    }

    if (moved) {
        lastGamepadInputTime = timestamp;
        draw();
    }
    
    // Soft drop
    if ((gp.buttons[13] && gp.buttons[13].pressed) || gp.axes[1] > 0.5) { // D-pad down or stick down
        update();
        draw();
        lastTimestamp = timestamp; // Reset drop timer
        lastGamepadInputTime = timestamp;
    }
}

// --- Main Game Loop ---

function gameLoop(timestamp) {
    if (gameState !== 'playing') return;
    
    handleGamepadInput(timestamp);

    const deltaTime = timestamp - lastTimestamp;
    if (deltaTime > dropInterval) {
        lastTimestamp = timestamp;
        update();
        draw();
    }

    gameLoopId = requestAnimationFrame(gameLoop);
}

function startGame() {
    newShape();
    lastTimestamp = performance.now();
    updateDisplay();
    gameLoopId = requestAnimationFrame(gameLoop);
}

// Initialize game without starting
function initGame() {
    updateDisplay();
    draw();
}

// Start the game initialization
initGame();

// Game Over Modal Functions
function showGameOverModal() {
    const modal = document.getElementById('game-over-modal');
    const settings = DIFFICULTY_SETTINGS[currentDifficulty];
    
    // Update modal content
    document.getElementById('final-difficulty').textContent = settings.name;
    document.getElementById('final-score').textContent = score;
    document.getElementById('final-level').textContent = level;
    
    // Check for new high score
    const newRecordElement = document.getElementById('new-record');
    if (score > highScore) {
        highScore = score;
        localStorage.setItem('tetrisHighScore', highScore);
        newRecordElement.style.display = 'block';
        updateDisplay(); // Update the high score display
    } else {
        newRecordElement.style.display = 'none';
    }
    
    // Show modal
    modal.classList.add('show');
}

function closeGameOverModal() {
    const modal = document.getElementById('game-over-modal');
    modal.classList.remove('show');
    resetGame();
}

function restartGame() {
    const modal = document.getElementById('game-over-modal');
    modal.classList.remove('show');
    startNewGame();
}