:root {
    --focus: orange;
    --bg: #fff;
    /*#0A001F;*/
    --fg: #000;
    --border: #eee;
    --primary: #4A90E2;
    --fg-primary: #eee;
    --bg-primary: #ECF5FF;
    --border-primary: #4A90E2;
    --pre-bg: #eee;
}

@media(prefers-color-scheme: dark) {
    :root {
        --focus: orange;
        --bg: #202023;
        --fg: #f8f8f8;
        --border: #555;
        --primary: #4A90E2;
        --fg-primary: #eee;
        --bg-primary: hsl(212, 80%, 30%);
        --border-primary: #4A90E2;
        --pre-bg: #474749;
    }
}

/* Works on Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--pre-bg) var(--bg);
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
    width: 12px;
}

*::-webkit-scrollbar-track {
    background: var(--bg);
}

*::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 20px;
}

body {
    margin: auto;
    padding: 8px;
    font-size: 16px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
    background-color: var(--bg);
    color: var(--fg)
}

a {
    color:var(--primary);
}

pre,
code {
    border-radius: 5px;
    background: var(--pre-bg);
    color: var(--fg);
    padding: 0.5em;
    border: 1px solid var(--border);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    font-weight: 400;
}

code {
    padding-top: 0.2em;
    padding-bottom: 0.2em;
    display: inline-block;
}

input[type="text"] {
    width: calc(100% - 56px);
    display: block;
    border: 1px solid var(--border);
    font-family: inherit;
    font-size: inherit;
    padding: 2px 6px;
    font-family: monospace;
}

.save {
    display:block;
    width:100%;
    padding: 1em 0;
    margin: 0;
    background-color: var(--primary);
    color: var(--fg-primary);
    text-align: center;
    text-decoration: none;
    line-height: 1em;
    border:none;
}

.status {
    display:block;
    width: auto;
    max-width: 50%;
    padding: 1em;
    text-align: center;
    text-decoration: none;
    line-height: 1em;
    background-color: transparent;
    position: fixed;
    right: 1em;
    top: 1em;
    border-radius: 5px;
    border: 1px solid;
    transition: opacity 0.5s ease-in-out;
    opacity: 0;
}
.status.success{
    background-color: #d4edda;
    border-color:#155724;
    color:#155724;
}
.status.error{
    background-color: #f8d7da;
    border-color:#721c24;
    color:#721c24;
}
.input-sizer {
    position: relative;
    width: calc(100% - 56px);
    display: block;
    resize: both;
    min-height: 40px;
    font-family: monospace;
    font-size: inherit;
    padding: 0;
}
.input-sizer textarea {
    position: absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
    font-family: inherit;
    font-size: inherit;
    padding: 1px 6px;
    margin:0;
    border: 1px solid var(--border);
}
.input-sizer::after {
  content: attr(data-value) ' ';
  visibility: hidden;
  white-space: pre;
  font-family: inherit;
  font-size: inherit;
}

.radio-container>label,
.checkbox-container>label,
.textbox-container,
.instructions,
.button-container {
    background: var(--bg);
    border: 1px solid var(--border);
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    position: relative;
}

.instructions {
    padding:20px;
    display:block;
}

.radio-container>label,
.checkbox-container>label,
.button-container > label {
    cursor: pointer;
    padding: 20px 20px 20px 65px;
    max-width: calc(100% - 100px);
}

label p{
    word-break: normal;
    white-space: normal;
    font-weight: normal;
}

.textbox-container,
.button-container {
    box-sizing: border-box;
    border-color: var(--border-primary);
    overflow:hidden;
    transition:all 0.3s ease-out;
}

.textbox-container > label,
.button-container > label {
    margin: 8px 20px;
    padding:0;
}

.textbox-container > input[type="text"],
.textbox-container > textarea,
.textbox-container > .input-sizer,
.button-container input,
.button-container button {
    margin:0 20px 20px 20px;
}

.radio-container>label,
.checkbox-container>label,
.textbox-container>label,
.button-container>label {
    display: inline-block;
    font-weight: 600;
    transition: .3s ease all;
    flex: 1 0 auto;
}
.textbox-container
.radio-container>label:hover,
.checkbox-container>label:hover,
.button-container {
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
}
.radio-container>label::before,
.checkbox-container>label::before {
    background: var(--bg-primary);
    border-radius: 50%;
    content:'';
    height: 30px;
    left: 20px;
    position: absolute;
    top: calc(50% - 15px);
    transition: .3s ease background-color;
    width: 30px;
}
.radio-container>input[type="radio"],
.checkbox-container>input[type="checkbox"] {
    position: absolute;
    visibility: hidden;
}
.radio-container>input[type="radio"]:checked+label,
.checkbox-container>input[type="checkbox"]:checked+label {
    background: var(--bg-primary);
    border-color: var(--primary);
}
.radio-container>input[type="radio"]:checked+label::before,
.checkbox-container>input[type="checkbox"]:checked+label::before {
    background-color: var(--primary);
    background-image:  url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48c3ZnIHdpZHRoPSIyNiIgaGVpZ2h0PSIyMCIgdmVyc2lvbj0iMS4xIiB2aWV3Qm94PSIyLjAyOTY4IC00MC4wOTAzIDI2IDIwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48IS0tR2VuZXJhdGVkIGJ5IElKU1ZHIChodHRwczovL2dpdGh1Yi5jb20vaWNvbmphci9JSlNWRyktLT48cGF0aCBkPSJNMjcuOTc0MywtMzYuMTI3MmMwLDAuNDQ2NDI4IC0wLjE1NjI1LDAuODI1ODkzIC0wLjQ2ODc1LDEuMTM4MzlsLTEyLjEyMDUsMTIuMTIwNWwtMi4yNzY3OSwyLjI3Njc5Yy0wLjMxMjUsMC4zMTI1IC0wLjY5MTk2NCwwLjQ2ODc1IC0xLjEzODM5LDAuNDY4NzVjLTAuNDQ2NDI4LDAgLTAuODI1ODkzLC0wLjE1NjI1IC0xLjEzODM5LC0wLjQ2ODc1bC0yLjI3Njc5LC0yLjI3Njc5bC02LjA2MDI3LC02LjA2MDI3Yy0wLjMxMjUsLTAuMzEyNSAtMC40Njg3NSwtMC42OTE5NjUgLTAuNDY4NzUsLTEuMTM4MzljMCwtMC40NDY0MjkgMC4xNTYyNSwtMC44MjU4OTMgMC40Njg3NSwtMS4xMzgzOWwyLjI3Njc5LC0yLjI3Njc5YzAuMzEyNSwtMC4zMTI1IDAuNjkxOTY1LC0wLjQ2ODc1IDEuMTM4MzksLTAuNDY4NzVjMC40NDY0MjksMCAwLjgyNTg5MywwLjE1NjI1IDEuMTM4MzksMC40Njg3NWw0LjkyMTg4LDQuOTM4NjJsMTAuOTgyMSwtMTAuOTk4OWMwLjMxMjUsLTAuMzEyNSAwLjY5MTk2NCwtMC40Njg3NSAxLjEzODM5LC0wLjQ2ODc1YzAuNDQ2NDI4LDAgMC44MjU4OTMsMC4xNTYyNSAxLjEzODM5LDAuNDY4NzVsMi4yNzY3OCwyLjI3Njc5YzAuMzEyNSwwLjMxMjUgMC40Njg3NSwwLjY5MTk2NCAwLjQ2ODc1LDEuMTM4MzlaIiB0cmFuc2Zvcm09InNjYWxlKDEuMDAxOTgpIiBmaWxsPSIjZmZmIj48L3BhdGg+PC9zdmc+');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 15px;
}

.radio-container>input[type="radio"]:disabled+label,
.checkbox-container>input[type="checkbox"]:disabled+label {
    background-color: var(--bg);
    border-color: var(--pre-bg);
    cursor: not-allowed;
    opacity: 0.7;
}

.radio-container>input[type="radio"]:disabled+label::before,
.checkbox-container>input[type="checkbox"]:disabled+label::before {
    background-color: var(--pre-bg);
}

.radio-container, .checkbox-container, .button-container {
    display:flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: stretch;

    --gap: 12px;
    margin: calc(-1 * var(--gap)) 0 calc(var(--gap) * 2) calc(-1 * var(--gap));
    width: calc(100% + var(--gap));

    overflow:hidden;
    transition:all 0.3s ease-out;
}

.button-container {
    margin: calc(-1 * var(--gap)) 0 calc(var(--gap) * 2) 0;
    width: 100%
}

.checkbox-container{
    margin-top:0;
    margin-bottom: 0;
}

.radio-container > *,
.checkbox-container > *,
.button-container > * {
    margin: var(--gap) 0 0 var(--gap);
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

.radio-container > h3,
.checkbox-container > h3,
.radio-container>p,
.checkbox-container>p {
    width: 100%;
}

.button-container input,
.button-container button {
    cursor: pointer;
}

input[type="file"] {
    display: none;
}

button {
    background-color: var(--primary);
    color: var(--fg-primary);
    border-style: none;
    border-radius: 5px;
    padding:8px 20px;
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

#spinner:before {
    content: '';
    position: fixed;
    top: 20px;
    right: 20px;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    border-radius: 50%;
    border-top: 2px solid var(--primary);
    border-right: 2px solid transparent;
    animation: spinner .6s linear infinite;
}

input[type="text"], textarea {
    background-color: var(--bg);
    color: var(--fg);
}

#downloadMode p{
    font-size: 0.85em;
}

summary > * {
    display: inline-block;
}