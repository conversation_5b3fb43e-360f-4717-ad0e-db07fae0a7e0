<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI验证码破解助手 - 测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .test-section h3 {
            margin: 0 0 15px 0;
            font-size: 18px;
            color: #e3f2fd;
        }

        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .test-link {
            display: block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .test-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .test-link h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
        }

        .test-link p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .instructions {
            background: rgba(33, 150, 243, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }

        .instructions h4 {
            margin: 0 0 10px 0;
            color: #bbdefb;
        }

        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }

        .instructions li {
            margin-bottom: 5px;
            font-size: 14px;
        }

        .status-demo {
            background: rgba(76, 175, 80, 0.2);
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #4CAF50;
        }

        .status-demo h4 {
            margin: 0 0 10px 0;
            color: #c8e6c9;
        }

        .step-preview {
            font-size: 12px;
            line-height: 1.6;
        }

        .step-preview div {
            margin-bottom: 3px;
            padding: 2px 0;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 14px;
            opacity: 0.8;
        }

        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #ff9800;
        }

        .warning h4 {
            margin: 0 0 10px 0;
            color: #fff3c4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI验证码破解助手</h1>
            <p>测试页面 - 验证插件功能</p>
        </div>

        <div class="test-section">
            <h3>📋 使用说明</h3>
            <div class="instructions">
                <h4>🔧 测试步骤：</h4>
                <ol>
                    <li>确保已安装并启用浏览器插件</li>
                    <li>在插件弹窗中输入您的Gemini API密钥</li>
                    <li>点击下方测试链接打开验证码页面</li>
                    <li>在验证码页面点击插件图标</li>
                    <li>点击"🚀 破解验证码"按钮开始测试</li>
                    <li>观察详细的执行状态和步骤提示</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 推荐测试链接</h3>
            <div class="test-links">
                <a href="https://www.google.com/recaptcha/api2/demo" target="_blank" class="test-link">
                    <h4>🎯 Google reCAPTCHA 官方演示</h4>
                    <p>✅ 有明确的成功提示："验证成功"</p>
                </a>
                <a href="https://recaptcha-demo.appspot.com/" target="_blank" class="test-link">
                    <h4>🔬 reCAPTCHA 测试站点</h4>
                    <p>✅ 显示详细的验证结果和状态</p>
                </a>
                <a href="https://developers.google.com/recaptcha/docs/display" target="_blank" class="test-link">
                    <h4>📚 Google 开发者文档示例</h4>
                    <p>✅ 包含多种reCAPTCHA类型的测试</p>
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3>🎮 实际应用测试</h3>
            <div class="test-links">
                <a href="https://accounts.google.com/signup" target="_blank" class="test-link">
                    <h4>📧 Google 账户注册</h4>
                    <p>✅ 真实环境，注册成功有明确反馈</p>
                </a>
                <a href="https://discord.com/register" target="_blank" class="test-link">
                    <h4>💬 Discord 注册</h4>
                    <p>✅ 验证成功后可继续注册流程</p>
                </a>
                <a href="https://www.reddit.com/register" target="_blank" class="test-link">
                    <h4>🔗 Reddit 注册</h4>
                    <p>✅ 验证通过后显示"继续"按钮</p>
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 调试专用测试</h3>
            <div class="test-links">
                <a href="https://2captcha.com/demo/recaptcha-v2" target="_blank" class="test-link">
                    <h4>🛠️ 2captcha 演示页面</h4>
                    <p>✅ 专业验证码测试，有详细状态显示</p>
                </a>
                <a href="https://anti-captcha.com/clients/demo/recaptcha-v2" target="_blank" class="test-link">
                    <h4>🔍 Anti-Captcha 演示</h4>
                    <p>✅ 验证结果清晰，适合调试</p>
                </a>
                <a href="https://nopecha.com/demo/recaptcha" target="_blank" class="test-link">
                    <h4>🎯 NopeCHA 测试页面</h4>
                    <p>✅ 简单直观的成功/失败反馈</p>
                </a>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 执行状态预览</h3>
            <div class="status-demo">
                <h4>插件将显示以下详细步骤：</h4>
                <div class="step-preview">
                    <div>🔍 步骤1: 检测reCAPTCHA验证码框架...</div>
                    <div>🖱️ 步骤2: 点击"我不是机器人"复选框...</div>
                    <div>⏳ 步骤3: 等待图像验证码网格加载...</div>
                    <div>📸 步骤4: 截取验证码图像区域...</div>
                    <div>🤖 步骤5: AI分析图像并获取位置...</div>
                    <div>🎯 步骤6: 开始点击目标图像...</div>
                    <div>📤 步骤7: 提交验证结果...</div>
                    <div>✅ 步骤8: 检查验证是否成功...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 如何判断成功</h3>
            <div class="status-demo">
                <h4>验证成功的标志：</h4>
                <div class="step-preview">
                    <div><strong>Google官方演示：</strong> 显示"验证成功"绿色提示</div>
                    <div><strong>注册页面：</strong> 验证码消失，可以继续填写表单</div>
                    <div><strong>插件状态：</strong> 显示"🎉 验证码破解成功！"</div>
                    <div><strong>控制台：</strong> 看到"验证码破解成功"日志</div>
                </div>
                <h4 style="margin-top: 15px;">验证失败的标志：</h4>
                <div class="step-preview">
                    <div><strong>页面反应：</strong> 验证码重新加载或显示错误</div>
                    <div><strong>插件状态：</strong> 显示"⚠️ 验证可能需要重试"</div>
                    <div><strong>控制台：</strong> 看到错误信息或超时提示</div>
                </div>
            </div>
        </div>

        <div class="warning">
            <h4>⚠️ 注意事项：</h4>
            <ul>
                <li>需要有效的Gemini API密钥才能使用AI识别功能</li>
                <li>某些网站可能有反自动化机制，成功率可能不是100%</li>
                <li>建议在测试环境中使用，避免在重要账户上频繁测试</li>
                <li>插件会在页面右上角显示详细的执行进度</li>
            </ul>
        </div>

        <div class="footer">
            <p>🚀 AI验证码破解助手 - 让验证码破解变得简单</p>
            <p>基于Gemini AI技术，支持详细的执行状态显示</p>
        </div>
    </div>

    <script>
        // 检查插件是否已安装
        window.addEventListener('load', function() {
            // 检测插件状态指示器
            setTimeout(() => {
                const indicator = document.getElementById('captcha-solver-indicator');
                if (indicator) {
                    console.log('✅ 插件已正确加载');
                } else {
                    console.log('⚠️ 插件可能未正确加载');
                }
            }, 2000);
        });
    </script>
</body>
</html>
