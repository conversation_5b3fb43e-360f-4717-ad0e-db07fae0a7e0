<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .button {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 4px;
        }
        .button:hover {
            background: #45a049;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h3>MarkDownload Test</h3>
    <div id="status">Loading...</div>
    <a href="#" class="button" id="testBtn">Test Extension</a>
    <a href="#" class="button" id="downloadBtn">Download as Markdown</a>
    
    <script src="../browser-polyfill.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const status = document.getElementById('status');
            const testBtn = document.getElementById('testBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            
            status.textContent = 'Extension loaded successfully!';
            
            testBtn.addEventListener('click', async function(e) {
                e.preventDefault();
                try {
                    const tabs = await browser.tabs.query({active: true, currentWindow: true});
                    status.textContent = `Current tab: ${tabs[0].title}`;
                } catch (error) {
                    status.textContent = `Error: ${error.message}`;
                }
            });
            
            downloadBtn.addEventListener('click', async function(e) {
                e.preventDefault();
                try {
                    const tabs = await browser.tabs.query({active: true, currentWindow: true});
                    const tab = tabs[0];
                    
                    // Send message to background script
                    browser.runtime.sendMessage({
                        type: 'test_download',
                        tab: tab
                    });
                    
                    status.textContent = 'Download request sent!';
                } catch (error) {
                    status.textContent = `Error: ${error.message}`;
                }
            });
        });
    </script>
</body>
</html>
