# 便携式文件复制/覆盖工具

## 🎯 功能特性
- **文件操作**：智能文件复制/覆盖，自动备份
- **文件夹操作**：整个文件夹复制/覆盖，适合项目管理
- **多版本备份**：带时间戳的历史版本管理
- **便携式设计**：可放置在任意位置使用
- **图形界面**：简单易用的三标签页设计

## 📁 文件结构
```
工具文件夹/
├── file_copier.py          # 主程序
├── 启动工具.bat            # 启动脚本
├── README_便携式文件复制工具.md  # 使用说明
└── backups/                # 备份文件夹（自动生成）
```

## 🚀 快速开始

### 📄 文件操作
1. **复制新文件**：选择源文件 → 点击"另存为" → 执行文件操作
2. **覆盖文件**：选择源文件 → 点击"选择文件" → 执行文件操作

### 📁 文件夹操作
1. **复制新文件夹**：选择源文件夹 → 点击"新建文件夹" → 执行文件夹操作
2. **覆盖文件夹**：选择源文件夹 → 点击"选择文件夹" → 执行文件夹操作

### 🔄 备份管理
- 切换到"备份管理"标签页查看/恢复历史版本
- 使用"快速恢复"按钮恢复最近的备份

## 📦 便携性特性

### 完全便携
- 所有文件相对于脚本位置存储
- 可复制到任意位置使用
- 设置和备份历史自动跟随

### 备份文件命名
```
原文件名_backup_YYYYMMDD_HHMMSS.扩展名

示例：
config.txt → config_backup_20250704_143052.txt
data.json → data_backup_20250704_151907.json
```

### 自动目录创建
- 备份目录不存在时自动创建
- 目标文件目录不存在时自动创建
- 支持深层目录结构

## ⚙️ 配置文件

### settings.json
```json
{
  "custom_backup_dir": "D:\\MyBackups"  // 自定义备份路径
}
```

### backup_history.json
```json
[
  {
    "timestamp": "2025-07-04 15:19:07",
    "source_file": "C:\\source\\file.txt",
    "target_file": "C:\\target\\file.txt",
    "backup_file": "C:\\tool\\backups\\file_backup_20250704_151907.txt",
    "backup_size": 1024
  }
]
```

## 🛡️ 安全特性

- **强制备份**：覆盖前必须备份
- **多版本保留**：不覆盖历史备份
- **操作确认**：重要操作前二次确认
- **详细日志**：完整的操作记录
- **错误处理**：友好的错误提示

## 📋 系统要求

- Python 3.6+
- tkinter（通常随Python安装）
- Windows/Linux/macOS

## 🔧 部署方式

### 方式1：直接使用
1. 将 `file_copier.py` 复制到目标位置
2. 双击运行或使用命令行

### 方式2：批处理启动（Windows）
创建 `run_copier.bat`：
```batch
@echo off
cd /d "%~dp0"
python file_copier.py
pause
```

### 方式3：创建快捷方式
- 右键 → 发送到 → 桌面快捷方式
- 可在任意位置快速启动

## 📝 更新日志

### v2.0 (2025-07-04)
- ✅ 添加便携式设计
- ✅ 自定义备份路径功能
- ✅ 智能复制/覆盖模式
- ✅ 三标签页界面设计
- ✅ 设置持久化保存

### v1.0
- 基础文件覆盖功能
- 简单备份机制

## 💡 使用技巧

1. **批量操作**：可以重复使用工具进行多个文件操作
2. **备份清理**：定期清理不需要的历史备份
3. **路径设置**：根据需要灵活切换备份位置
4. **快速恢复**：使用"快速恢复"按钮恢复最近的备份
5. **便携部署**：将整个工具文件夹复制到U盘随身携带

## ❓ 常见问题

**Q: 工具可以放在任意位置吗？**
A: 是的，完全便携式设计，可以放在任意位置使用。

**Q: 备份文件会占用很多空间吗？**
A: 可以在"备份管理"中查看和清理不需要的备份。

**Q: 如何恢复到更早的版本？**
A: 在"备份管理"标签页中选择对应的历史备份进行恢复。

**Q: 自定义备份路径的设置会保存吗？**
A: 是的，设置会保存在settings.json文件中，重启后自动加载。
