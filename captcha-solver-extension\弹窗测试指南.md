# 🔧 弹窗测试指南

## 🚨 问题诊断

如果弹窗没有弹出，请按以下步骤检查：

### 1. 检查插件是否正确安装
1. 打开 `chrome://extensions/`
2. 确认"AI验证码破解助手"插件已启用
3. 检查是否有错误提示

### 2. 检查弹窗文件
- 确认 `popup-simple.html` 文件存在
- 确认 `popup-simple.js` 文件存在
- 确认 `manifest.json` 中指向正确的弹窗文件

### 3. 重新加载插件
1. 在扩展程序页面点击"重新加载"按钮
2. 或者先移除插件，再重新加载

### 4. 检查控制台错误
1. 在扩展程序页面点击"检查视图"
2. 查看是否有JavaScript错误

## 🧪 测试步骤

### 基本弹窗测试
1. **安装插件**
   ```
   1. 打开 chrome://extensions/
   2. 开启开发者模式
   3. 点击"加载已解压的扩展程序"
   4. 选择 captcha-solver-extension 文件夹
   ```

2. **测试弹窗**
   ```
   1. 查看浏览器工具栏是否有 🤖 图标
   2. 点击 🤖 图标
   3. 应该弹出紫色渐变的弹窗界面
   ```

3. **测试基本功能**
   ```
   1. 在弹窗中输入测试API密钥
   2. 点击"💾 保存设置"
   3. 应该显示"设置保存成功！"
   ```

### 执行状态测试
1. **访问测试页面**
   ```
   打开: https://www.google.com/recaptcha/api2/demo
   ```

2. **测试检测功能**
   ```
   1. 点击插件图标打开弹窗
   2. 点击"🔍 检测验证码"
   3. 应该显示"发现验证码！"
   ```

3. **测试破解功能**
   ```
   1. 输入有效的Gemini API密钥
   2. 点击"🚀 破解验证码"
   3. 观察执行状态区域的显示
   ```

## 📊 预期的执行状态显示

当点击"破解验证码"后，弹窗应该显示：

### 进度信息
- 当前步骤文本
- 百分比进度
- 进度条动画
- 详细状态信息

### 步骤列表
```
⏳ 检测验证码
⏳ 点击复选框  
⏳ 等待加载
⏳ 截取图像
⏳ AI分析
⏳ 点击图像
⏳ 提交验证
⏳ 检查结果
```

执行过程中图标会变化：
- ⏳ = 等待执行
- 🔄 = 正在执行  
- ✅ = 执行成功
- ❌ = 执行失败

## 🔍 故障排除

### 弹窗不显示
1. **检查manifest.json**
   ```json
   "action": {
     "default_popup": "popup-simple.html",
     "default_title": "🤖 AI验证码破解助手"
   }
   ```

2. **检查文件路径**
   ```
   captcha-solver-extension/
   ├── manifest.json
   ├── popup-simple.html  ← 确认存在
   ├── popup-simple.js    ← 确认存在
   └── ...
   ```

3. **重新安装插件**
   ```
   1. 在扩展程序页面移除插件
   2. 重新加载插件文件夹
   3. 确认插件图标出现在工具栏
   ```

### 弹窗显示但功能不工作
1. **检查控制台**
   ```
   1. 右键点击插件图标
   2. 选择"检查弹出式窗口"
   3. 查看Console标签的错误信息
   ```

2. **检查权限**
   ```json
   "permissions": [
     "activeTab",
     "storage", 
     "scripting"
   ]
   ```

### 执行状态不更新
1. **检查消息传递**
   ```javascript
   // 在popup中应该能收到消息
   chrome.runtime.onMessage.addListener((message) => {
     console.log('收到消息:', message);
   });
   ```

2. **检查content script**
   ```javascript
   // content.js应该发送消息
   chrome.runtime.sendMessage({
     action: 'updateStatus',
     message: '步骤1: 检测验证码...'
   });
   ```

## 📞 获取帮助

如果仍然有问题：

1. **查看浏览器控制台**
   - 按F12打开开发者工具
   - 查看Console标签的错误信息

2. **检查插件控制台**
   - 在扩展程序页面点击"检查视图"
   - 查看popup和background的错误

3. **确认Chrome版本**
   - 需要Chrome 88+支持Manifest V3

4. **重启浏览器**
   - 有时需要重启Chrome才能生效
