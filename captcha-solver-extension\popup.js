// popup.js - 弹窗界面逻辑
console.log('🚀 Popup script loading...');
console.log('📄 Document ready state:', document.readyState);
console.log('🌐 Chrome APIs available:', {
    storage: !!chrome.storage,
    tabs: !!chrome.tabs,
    runtime: !!chrome.runtime
});

// 确保DOM完全加载后再执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePopup);
} else {
    // 如果已经加载完成，直接执行
    setTimeout(initializePopup, 100);
}

function initializePopup() {
    console.log('📋 DOM Content Loaded');
    console.log('🔍 开始检查DOM元素...');

    try {
        // 检查所有必需的元素是否存在
        const apiKeyInput = document.getElementById('apiKey');
        const autoModeToggle = document.getElementById('autoMode');
        const solveBtn = document.getElementById('solveBtn');
        const detectBtn = document.getElementById('detectBtn');
        const saveBtn = document.getElementById('saveBtn');
        const resetBtn = document.getElementById('resetBtn');
        const status = document.getElementById('status');

        // 验证所有元素都存在
        const elements = {
            apiKeyInput,
            autoModeToggle,
            solveBtn,
            detectBtn,
            saveBtn,
            resetBtn,
            status
        };

        console.log('🔍 元素检查结果:', elements);

        const missingElements = [];
        for (const [name, element] of Object.entries(elements)) {
            if (!element) {
                missingElements.push(name);
                console.error(`❌ 缺少元素: ${name}`);
            } else {
                console.log(`✅ 找到元素: ${name}`);
            }
        }

        if (missingElements.length > 0) {
            console.error('❌ 缺少DOM元素:', missingElements);
            console.log('📋 当前DOM结构:', document.body.innerHTML);

            // 创建一个临时状态显示
            const tempStatus = document.createElement('div');
            tempStatus.textContent = `初始化失败: 缺少元素 ${missingElements.join(', ')}`;
            tempStatus.style.cssText = `
                color: red;
                background: #ffebee;
                padding: 10px;
                margin: 10px;
                text-align: center;
                border-radius: 5px;
                border: 1px solid #f44336;
                font-size: 12px;
            `;
            document.body.appendChild(tempStatus);
            return;
        }

        console.log('✅ All elements found');

        // 显示初始状态
        showStatus('插件已加载', 'info', elements.status);

        // 加载保存的设置
        loadSettings(elements);

        // 事件监听器
        elements.solveBtn.addEventListener('click', () => solveCaptcha(elements));
        elements.detectBtn.addEventListener('click', () => detectCaptcha(elements));
        elements.saveBtn.addEventListener('click', () => saveSettings(elements));
        elements.resetBtn.addEventListener('click', () => resetSettings(elements));

        console.log('🎯 Event listeners attached');

    } catch (error) {
        console.error('💥 Popup initialization error:', error);
        // 如果有status元素，显示错误
        const statusEl = document.getElementById('status');
        if (statusEl) {
            statusEl.textContent = '初始化失败: ' + error.message;
            statusEl.className = 'status error';
            statusEl.style.display = 'block';
        } else {
            // 如果连status元素都没有，创建一个临时的错误显示
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
                color: red;
                background: #ffebee;
                padding: 10px;
                margin: 10px;
                border-radius: 5px;
                border: 1px solid #f44336;
                text-align: center;
                font-size: 12px;
            `;
            errorDiv.textContent = '初始化失败: ' + error.message;
            document.body.appendChild(errorDiv);
        }
    }

    // 加载设置
    async function loadSettings(elements) {
        try {
            console.log('📥 Loading settings...');
            const result = await chrome.storage.sync.get(['apiKey', 'autoMode']);
            console.log('💾 Settings loaded:', result);

            if (result.apiKey) {
                elements.apiKeyInput.value = result.apiKey;
                console.log('🔑 API key loaded');
            }
            elements.autoModeToggle.checked = result.autoMode || false;
            console.log('⚙️ Auto mode:', result.autoMode);

            showStatus('设置加载完成', 'success', elements.status);
        } catch (error) {
            console.error('❌ Load settings error:', error);
            showStatus('加载设置失败: ' + error.message, 'error', elements.status);
        }
    }

    // 保存设置
    async function saveSettings(elements) {
        try {
            const apiKey = elements.apiKeyInput.value.trim();
            if (!apiKey) {
                showStatus('请输入API密钥', 'error', elements.status);
                return;
            }

            await chrome.storage.sync.set({
                apiKey: apiKey,
                autoMode: elements.autoModeToggle.checked
            });

            showStatus('设置保存成功！', 'success', elements.status);
        } catch (error) {
            showStatus('保存设置失败', 'error', elements.status);
        }
    }

    // 重置设置
    async function resetSettings(elements) {
        try {
            await chrome.storage.sync.clear();
            elements.apiKeyInput.value = '';
            elements.autoModeToggle.checked = false;
            showStatus('设置已重置', 'info', elements.status);
        } catch (error) {
            showStatus('重置失败', 'error', elements.status);
        }
    }

    // 检测验证码
    async function detectCaptcha(elements) {
        try {
            showStatus('正在检测验证码...', 'info', elements.status);

            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});

            const result = await chrome.tabs.sendMessage(tab.id, {
                action: 'detectCaptcha'
            });

            if (result.success) {
                showStatus(`检测到 ${result.count} 个验证码`, 'success', elements.status);
            } else {
                showStatus('未检测到验证码', 'info', elements.status);
            }
        } catch (error) {
            showStatus('检测失败: ' + error.message, 'error', elements.status);
        }
    }

    // 破解验证码
    async function solveCaptcha(elements) {
        try {
            const apiKey = elements.apiKeyInput.value.trim();
            if (!apiKey) {
                showStatus('请先输入API密钥', 'error', elements.status);
                return;
            }

            // 显示执行状态区域
            const executionStatus = document.getElementById('executionStatus');
            if (executionStatus) {
                executionStatus.classList.add('active');
                resetStepList();
            }

            showStatus('正在破解验证码...', 'info', elements.status);
            elements.solveBtn.textContent = '🔄 破解中...';
            elements.solveBtn.disabled = true;

            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});

            const result = await chrome.tabs.sendMessage(tab.id, {
                action: 'solveCaptcha',
                apiKey: apiKey
            });

            if (result.success) {
                showStatus('验证码破解成功！', 'success', elements.status);
            } else {
                showStatus('破解失败: ' + result.error, 'error', elements.status);
            }
        } catch (error) {
            showStatus('破解失败: ' + error.message, 'error', elements.status);
        } finally {
            elements.solveBtn.textContent = '🚀 破解验证码';
            elements.solveBtn.disabled = false;
        }
    }

    // 显示状态信息
    function showStatus(message, type, statusElement) {
        try {
            if (!statusElement) {
                console.error('❌ 状态元素未提供');
                return;
            }

            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.style.display = 'block';

            console.log(`📢 状态更新: ${message} (${type})`);

            // 3秒后自动隐藏
            setTimeout(() => {
                if (statusElement && statusElement.style) {
                    statusElement.style.display = 'none';
                }
            }, 3000);
        } catch (error) {
            console.error('❌ showStatus 错误:', error);
        }
    }

    // 监听来自content script的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'updateStatus') {
            const statusElement = document.getElementById('status');
            const solveBtn = document.getElementById('solveBtn');

            if (statusElement) {
                showStatus(message.message, message.type, statusElement);
            }

            // 更新执行状态显示
            updateExecutionStatus(message);

            // 如果是步骤更新，也更新按钮状态
            if (message.message.includes('步骤') && solveBtn) {
                if (message.type === 'info') {
                    solveBtn.textContent = '🔄 ' + message.message.split(':')[0];
                    solveBtn.disabled = true;
                } else if (message.type === 'success' && message.message.includes('验证码破解成功')) {
                    solveBtn.textContent = '🚀 破解验证码';
                    solveBtn.disabled = false;
                } else if (message.type === 'error') {
                    solveBtn.textContent = '🚀 破解验证码';
                    solveBtn.disabled = false;
                }
            }
        }
    });

    // 更新执行状态显示
    function updateExecutionStatus(message) {
        const executionStatus = document.getElementById('executionStatus');
        const currentStepText = document.getElementById('currentStepText');
        const stepPercentage = document.getElementById('stepPercentage');
        const progressFill = document.getElementById('progressFill');
        const stepDetails = document.getElementById('stepDetails');

        if (!executionStatus) return;

        // 显示执行状态区域
        if (message.message.includes('步骤') || message.message.includes('破解')) {
            executionStatus.classList.add('active');
        }

        // 更新当前步骤文本
        if (currentStepText) {
            currentStepText.textContent = message.message;
            currentStepText.className = `step-text step-${message.type}`;
        }

        // 更新进度百分比
        if (stepPercentage && message.percentage !== undefined) {
            stepPercentage.textContent = `${message.percentage}%`;
        }

        // 更新进度条
        if (progressFill && message.percentage !== undefined) {
            progressFill.style.width = `${message.percentage}%`;
        }

        // 更新详细信息
        if (stepDetails && message.details) {
            stepDetails.textContent = message.details;
        }

        // 更新步骤列表状态
        updateStepList(message.currentStep, message.type);

        // 如果完成或出错，延迟隐藏状态区域
        if (message.type === 'success' && message.message.includes('验证码破解成功')) {
            setTimeout(() => {
                executionStatus.classList.remove('active');
                resetStepList();
            }, 5000);
        } else if (message.type === 'error') {
            setTimeout(() => {
                executionStatus.classList.remove('active');
                resetStepList();
            }, 8000);
        }
    }

    // 更新步骤列表状态
    function updateStepList(currentStep, type) {
        if (!currentStep) return;

        for (let i = 1; i <= 8; i++) {
            const stepElement = document.getElementById(`step${i}`);
            if (!stepElement) continue;

            const iconElement = stepElement.querySelector('.step-icon');

            if (i < currentStep) {
                // 已完成的步骤
                stepElement.className = 'step-item completed';
                iconElement.textContent = '✅';
            } else if (i === currentStep) {
                // 当前步骤
                if (type === 'error') {
                    stepElement.className = 'step-item error';
                    iconElement.textContent = '❌';
                } else if (type === 'success') {
                    stepElement.className = 'step-item completed';
                    iconElement.textContent = '✅';
                } else {
                    stepElement.className = 'step-item current';
                    iconElement.textContent = '🔄';
                }
            } else {
                // 待执行的步骤
                stepElement.className = 'step-item pending';
                iconElement.textContent = '⏳';
            }
        }
    }

    // 重置步骤列表
    function resetStepList() {
        for (let i = 1; i <= 8; i++) {
            const stepElement = document.getElementById(`step${i}`);
            if (stepElement) {
                stepElement.className = 'step-item pending';
                const iconElement = stepElement.querySelector('.step-icon');
                if (iconElement) {
                    iconElement.textContent = '⏳';
                }
            }
        }

        // 重置其他元素
        const currentStepText = document.getElementById('currentStepText');
        const stepPercentage = document.getElementById('stepPercentage');
        const progressFill = document.getElementById('progressFill');
        const stepDetails = document.getElementById('stepDetails');

        if (currentStepText) currentStepText.textContent = '准备开始...';
        if (stepPercentage) stepPercentage.textContent = '0%';
        if (progressFill) progressFill.style.width = '0%';
        if (stepDetails) stepDetails.textContent = '等待开始执行...';
    }
}

console.log('📝 Popup script loaded completely');
