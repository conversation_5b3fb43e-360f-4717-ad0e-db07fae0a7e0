<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 380px;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .config-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        
        .config-section h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #e0e0e0;
        }
        
        .input-group {
            margin-bottom: 10px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #f0f0f0;
        }
        
        .input-group input {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 12px;
            box-sizing: border-box;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 12px;
            display: none;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            color: #c8e6c9;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.3);
            color: #ffcdd2;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.3);
            color: #bbdefb;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.3);
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #4CAF50;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* 执行状态显示区域 */
        .execution-status {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
            display: none;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .execution-status.active {
            display: block;
        }

        .execution-status h4 {
            margin: 0 0 10px 0;
            font-size: 13px;
            color: #e3f2fd;
        }

        .step-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .step-text {
            font-size: 12px;
            font-weight: 500;
        }

        .step-percentage {
            font-size: 11px;
            color: #bbdefb;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .step-details {
            font-size: 11px;
            color: #e0e0e0;
            line-height: 1.4;
            min-height: 16px;
            margin-bottom: 10px;
        }

        .step-list {
            font-size: 10px;
        }

        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
            padding: 2px 0;
        }

        .step-icon {
            margin-right: 6px;
            width: 12px;
            text-align: center;
        }

        .step-item.completed .step-icon {
            color: #4CAF50;
        }

        .step-item.current .step-icon {
            color: #2196F3;
        }

        .step-item.pending .step-icon {
            color: #666;
        }

        .step-item.error .step-icon {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI验证码破解助手</h1>
    </div>
    
    <div class="config-section">
        <h3>⚙️ API配置</h3>
        <div class="input-group">
            <label for="apiKey">Gemini API密钥:</label>
            <input type="password" id="apiKey" placeholder="输入你的Gemini API密钥">
        </div>
        <div class="input-group">
            <label for="autoMode">自动模式:</label>
            <label class="toggle-switch">
                <input type="checkbox" id="autoMode">
                <span class="slider"></span>
            </label>
        </div>
    </div>
    
    <div class="config-section">
        <h3>🎯 操作控制</h3>
        <div class="button-group">
            <button class="btn btn-primary" id="solveBtn">🚀 破解验证码</button>
            <button class="btn btn-secondary" id="detectBtn">🔍 检测验证码</button>
        </div>
        <div class="button-group">
            <button class="btn btn-secondary" id="saveBtn">💾 保存设置</button>
            <button class="btn btn-secondary" id="resetBtn">🔄 重置</button>
        </div>

        <!-- 执行状态显示区域 -->
        <div id="executionStatus" class="execution-status">
            <h4>📊 执行状态</h4>
            <div class="step-info">
                <div class="step-text" id="currentStepText">准备开始...</div>
                <div class="step-percentage" id="stepPercentage">0%</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="step-details" id="stepDetails">等待开始执行...</div>

            <!-- 详细步骤列表 -->
            <div class="step-list">
                <div class="step-item pending" id="step1">
                    <div class="step-icon">⏳</div>
                    <div>🔍 检测reCAPTCHA验证码框架</div>
                </div>
                <div class="step-item pending" id="step2">
                    <div class="step-icon">⏳</div>
                    <div>🖱️ 点击"我不是机器人"复选框</div>
                </div>
                <div class="step-item pending" id="step3">
                    <div class="step-icon">⏳</div>
                    <div>⏳ 等待图像验证码网格加载</div>
                </div>
                <div class="step-item pending" id="step4">
                    <div class="step-icon">⏳</div>
                    <div>📸 截取验证码图像区域</div>
                </div>
                <div class="step-item pending" id="step5">
                    <div class="step-icon">⏳</div>
                    <div>🤖 AI分析图像并获取位置</div>
                </div>
                <div class="step-item pending" id="step6">
                    <div class="step-icon">⏳</div>
                    <div>🎯 点击识别出的目标图像</div>
                </div>
                <div class="step-item pending" id="step7">
                    <div class="step-icon">⏳</div>
                    <div>📤 提交验证结果</div>
                </div>
                <div class="step-item pending" id="step8">
                    <div class="step-icon">⏳</div>
                    <div>✅ 检查验证是否成功</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="status" id="status"></div>
    
    <script src="popup.js"></script>
</body>
</html>
