<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 15px;
            font-family: Arial, sans-serif;
        }
        .button {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        .button:hover {
            background: #45a049;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 12px;
        }
        .info {
            background: #e7f3ff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <h3>MarkDownload 简化测试</h3>
    <div class="info">
        <strong>💡 说明：</strong>这是简化测试版本。请在普通网页（如 Wikipedia）上测试，不要在浏览器内部页面使用。
    </div>
    <div id="status">正在初始化...</div>
    <button class="button" id="testBtn">测试扩展</button>
    <button class="button" id="testScriptBtn">测试脚本注入</button>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const status = document.getElementById('status');
            const testBtn = document.getElementById('testBtn');
            const testScriptBtn = document.getElementById('testScriptBtn');
            
            status.textContent = '扩展已加载，准备测试';
            
            // Check if current tab is accessible
            async function checkTabAccess() {
                try {
                    const tabs = await chrome.tabs.query({active: true, currentWindow: true});
                    const tab = tabs[0];
                    
                    if (tab.url.startsWith('chrome://') || 
                        tab.url.startsWith('chrome-extension://') ||
                        tab.url.startsWith('edge://') ||
                        tab.url.startsWith('about:')) {
                        return {
                            accessible: false,
                            tab: tab,
                            reason: '浏览器内部页面 - 扩展无法访问此页面'
                        };
                    }
                    
                    return {
                        accessible: true,
                        tab: tab
                    };
                } catch (error) {
                    return {
                        accessible: false,
                        reason: error.message
                    };
                }
            }

            testBtn.addEventListener('click', async function(e) {
                e.preventDefault();
                const result = await checkTabAccess();
                
                if (result.accessible) {
                    status.textContent = `✅ 当前页面可访问: ${result.tab.title}`;
                    status.style.background = '#d4edda';
                    status.style.color = '#155724';
                } else {
                    status.textContent = `❌ ${result.reason}`;
                    status.style.background = '#f8d7da';
                    status.style.color = '#721c24';
                }
            });
            
            testScriptBtn.addEventListener('click', async function(e) {
                e.preventDefault();
                const result = await checkTabAccess();
                
                if (!result.accessible) {
                    status.textContent = `❌ 无法测试: ${result.reason}`;
                    status.style.background = '#f8d7da';
                    status.style.color = '#721c24';
                    return;
                }
                
                try {
                    status.textContent = '🔄 正在测试脚本注入...';
                    status.style.background = '#fff3cd';
                    status.style.color = '#856404';
                    
                    // Test script injection
                    const scriptResult = await chrome.scripting.executeScript({
                        target: { tabId: result.tab.id },
                        func: () => {
                            if (typeof simpleGetPageInfo === 'function') {
                                return simpleGetPageInfo();
                            }
                            return { error: 'simpleGetPageInfo 函数未找到' };
                        }
                    });
                    
                    const data = scriptResult[0].result;
                    if (data && !data.error) {
                        status.textContent = `✅ 成功! 页面: "${data.title}" (状态: ${data.readyState})`;
                        status.style.background = '#d4edda';
                        status.style.color = '#155724';
                    } else {
                        status.textContent = `❌ 脚本错误: ${data.error || '未知错误'}`;
                        status.style.background = '#f8d7da';
                        status.style.color = '#721c24';
                    }
                } catch (error) {
                    status.textContent = `❌ 注入失败: ${error.message}`;
                    status.style.background = '#f8d7da';
                    status.style.color = '#721c24';
                    console.error('Script injection error:', error);
                }
            });
        });
    </script>
</body>
</html>
