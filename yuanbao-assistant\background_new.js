﻿/**
 * 鍏冨疂鍔╂墜 - 鑳屾櫙鑴氭湰
 * 澶勭悊鎵╁睍鐨勫悗鍙伴€昏緫銆佺姸鎬佺鐞嗗拰澶栭儴閫氫俊
 */

// 鐘舵€佺鐞? 璁板綍姣忎釜鏍囩椤电殑鐘舵€?
let tabStates = {};
const DEBUG_MODE = true;

// 璋冭瘯鏃ュ織鍑芥暟
function debug(message, data = null) {
  if (!DEBUG_MODE) return;
  
  const prefix = '鍏冨疂鍔╂墜鑳屾櫙: ';
  if (data) {
    console.log(prefix + message, data);
  } else {
    console.log(prefix + message);
  }
}

// 鍒濆鍖?
debug('鑳屾櫙鑴氭湰宸插姞杞?);

// 鐩戝惉鍐呭鑴氭湰娑堟伅
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  debug('鏀跺埌娑堟伅', request);
  
  // 纭繚娑堟伅鏉ヨ嚜鍐呭鑴氭湰
  if (!sender.tab && request.action !== 'getStatus' && request.action !== 'optimize' && request.action !== 'injectContentScript') {
    debug('娑堟伅娌℃湁鍏宠仈鐨勬爣绛鹃〉锛屽拷鐣?);
    return false;
  }
  
  const tabId = sender.tab ? sender.tab.id : (request.tabId || null);
  
  switch(request.action) {
    case 'answerDetected':
      // 褰撳唴瀹硅剼鏈娴嬪埌鍏冨疂鍥炵瓟鏃?
      debug('鏀跺埌鍥炵瓟妫€娴嬮€氱煡锛屾潵鑷爣绛?' + tabId);
      handleAnswerDetected(tabId);
      sendResponse({success: true});
      break;
      
    case 'getStatus':
      // 褰撳脊鍑虹獥鍙ｈ姹傜姸鎬佹椂
      debug('鏀跺埌鐘舵€佽姹傦紝鏍囩 ' + tabId);
      const status = getTabState(request.tabId || tabId);
      debug('杩斿洖鐘舵€?, status);
      sendResponse(status);
      break;
      
    case 'optimize':
      // 褰撳脊鍑虹獥鍙ｈ姹備紭鍖栧洖绛旀椂
      debug('鏀跺埌浼樺寲璇锋眰', request);
      optimizeAnswer(request.tabId, request.answer, request.prompt, request.model)
        .then(result => {
          debug('浼樺寲瀹屾垚锛屽彂閫佺粨鏋?);
          sendResponse({success: true, result: result});
        })
        .catch(error => {
          debug('浼樺寲澶辫触', error);
          sendResponse({success: false, error: error.message});
        });
      return true; // 淇濇寔娑堟伅閫氶亾寮€鏀撅紝鐢ㄤ簬寮傛鍝嶅簲
      
    case 'injectContentScript':
      // 鎵嬪姩鍚戞寚瀹氭爣绛炬敞鍏ュ唴瀹硅剼鏈?
      debug('璇锋眰鍚戞爣绛炬敞鍏ュ唴瀹硅剼鏈? ' + request.tabId);
      injectContentScriptToTab(request.tabId)
        .then(result => {
          sendResponse({success: true, result: result});
        })
        .catch(error => {
          debug('鑴氭湰娉ㄥ叆澶辫触', error);
          sendResponse({success: false, error: error.message});
        });
      return true; // 淇濇寔娑堟伅閫氶亾寮€鏀撅紝鐢ㄤ簬寮傛鍝嶅簲
  }
  
  return true;
});

// 褰撴爣绛鹃〉鏇存柊鏃讹紝閲嶇疆鍏剁姸鎬?
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
  // 浠呭湪椤甸潰瀹屽叏鍔犺浇鏃堕噸缃姸鎬?
  if (changeInfo.status === 'complete') {
    debug('鏍囩椤靛凡鏇存柊锛岄噸缃姸鎬? ' + tabId);
    resetTabState(tabId);
    
    // 鍚戝唴瀹硅剼鏈彂閫侀〉闈㈠凡鍔犺浇鐨勯€氱煡
    setTimeout(() => {
      try {
        chrome.tabs.sendMessage(tabId, { action: 'pageLoaded' }, function(response) {
          // 濡傛灉鍙戦€佹秷鎭け璐ワ紝鍙兘鍐呭鑴氭湰鏈姞杞斤紝灏濊瘯娉ㄥ叆
          if (chrome.runtime.lastError) {
            debug('鍚戝唴瀹硅剼鏈彂閫佹秷鎭け璐ワ紝鍙兘闇€瑕佹敞鍏ヨ剼鏈?, chrome.runtime.lastError);
            // 浠呭http鍜宧ttps椤甸潰鎵ц娉ㄥ叆 (閬垮厤瀵筩hrome://椤甸潰绛夊皾璇曟敞鍏?
            if (tab.url && (tab.url.startsWith('http://') || tab.url.startsWith('https://'))) {
              injectContentScriptToTab(tabId);
            }
          }
        });
      } catch (error) {
        debug('椤甸潰鍔犺浇閫氱煡澶辫触', error);
      }
    }, 1000);
  }
});

// 褰撴爣绛鹃〉鍏抽棴鏃讹紝娓呯悊鍏剁姸鎬?
chrome.tabs.onRemoved.addListener(function(tabId) {
  debug('鏍囩椤靛凡鍏抽棴锛屾竻鐞嗙姸鎬? ' + tabId);
  delete tabStates[tabId];
});

/**
 * 鍚戞寚瀹氭爣绛鹃〉娉ㄥ叆鍐呭鑴氭湰
 * @param {number} tabId - 鏍囩椤礗D
 * @returns {Promise} 娉ㄥ叆缁撴灉
 */
async function injectContentScriptToTab(tabId) {
  debug('寮€濮嬪悜鏍囩椤垫敞鍏ュ唴瀹硅剼鏈? ' + tabId);
  
  try {
    // 纭鏍囩椤靛瓨鍦ㄤ笖鏈夋晥
    const tab = await chrome.tabs.get(tabId).catch(() => null);
    if (!tab) {
      throw new Error('鏍囩椤典笉瀛樺湪鎴栧凡鍏抽棴');
    }
    
    // 纭鏄綉椤佃€屼笉鏄壒娈婇〉闈?
    if (!tab.url || !(tab.url.startsWith('http://') || tab.url.startsWith('https://'))) {
      throw new Error('鏍囩椤典笉鏄櫘閫氱綉椤碉紝鏃犳硶娉ㄥ叆鑴氭湰');
    }
    
    // 鍏堟敞鍏SS
    await chrome.scripting.insertCSS({
      target: { tabId: tabId },
      files: ['content.css']
    });
    debug('CSS娉ㄥ叆鎴愬姛');
    
    // 鐒跺悗娉ㄥ叆JavaScript
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['content.js']
    });
    debug('JavaScript娉ㄥ叆鎴愬姛');
    
    // 纭鑴氭湰鏄惁姝ｅ父鍒濆鍖?
    setTimeout(async () => {
      try {
        // 灏濊瘯ping鍐呭鑴氭湰
        await chrome.tabs.sendMessage(tabId, { action: 'pingContentScript' });
        debug('鍐呭鑴氭湰鍝嶅簲姝ｅ父');
      } catch (error) {
        debug('鍐呭鑴氭湰鏃犲搷搴?, error);
        // 杩欓噷涓嶉渶瑕佸鐞嗭紝鍥犱负鎴戜滑鍙槸鍋氭鏌?
      }
    }, 500);
    
    return { success: true, message: '鍐呭鑴氭湰娉ㄥ叆鎴愬姛' };
  } catch (error) {
    debug('娉ㄥ叆鑴氭湰澶辫触', error);
    throw error;
  }
}

/**
 * 璁剧疆鍥炬爣鐘舵€?(涓存椂浣跨敤寰界珷浠ｆ浛鍥炬爣)
 */
function setIconState(tabId, state) {
  let badgeText = '';
  let badgeColor = '';
  
  switch(state) {
    case 'waiting':
      badgeText = '绛夊緟';
      badgeColor = '#777777';
      break;
      
    case 'detected':
      badgeText = '妫€娴?;
      badgeColor = '#FFC107';
      break;
      
    case 'processing':
      badgeText = '澶勭悊';
      badgeColor = '#2196F3';
      break;
      
    case 'completed':
      badgeText = '瀹屾垚';
      badgeColor = '#4CAF50';
      break;
      
    case 'error':
      badgeText = '閿欒';
      badgeColor = '#F44336';
      break;
      
    default:
      badgeText = '绛夊緟';
      badgeColor = '#777777';
  }
  
  try {
    // 浣跨敤寰界珷棰滆壊鍜屾枃鏈唬鏇垮浘鏍?
    chrome.action.setBadgeText({ tabId: tabId, text: badgeText });
    chrome.action.setBadgeBackgroundColor({ tabId: tabId, color: badgeColor });
  } catch (error) {
    debug('璁剧疆鐘舵€佹爣璇嗘椂鍑洪敊', error);
  }
}

/**
 * 澶勭悊妫€娴嬪埌鍏冨疂鍥炵瓟鐨勪簨浠?
 */
function handleAnswerDetected(tabId) {
  debug('澶勭悊鍥炵瓟妫€娴嬩簨浠讹紝鏍囩: ' + tabId);
  
  // 濡傛灉鏍囩椤靛凡缁忓浜庨潪绛夊緟鐘舵€侊紝涓嶅啀鏀瑰彉
  const currentState = getTabState(tabId);
  if (currentState.status && currentState.status !== 'waiting') {
    debug('鏍囩宸插湪闈炵瓑寰呯姸鎬侊紝淇濇寔褰撳墠鐘舵€? ' + currentState.status);
    return;
  }
  
  // 鏇存柊鐘舵€?
  updateTabState(tabId, 'detected');
}

/**
 * 鑾峰彇鏍囩椤电姸鎬?
 */
function getTabState(tabId) {
  if (!tabId) {
    return { status: 'waiting' };
  }
  
  if (!tabStates[tabId]) {
    tabStates[tabId] = { status: 'waiting' };
  }
  return tabStates[tabId];
}

/**
 * 鏇存柊鏍囩椤电姸鎬?
 */
function updateTabState(tabId, status, data = {}) {
  debug('鏇存柊鏍囩鐘舵€?, { tabId, status, data });
  
  if (!tabId) {
    debug('鏃犳晥鐨勬爣绛綢D锛屾棤娉曟洿鏂扮姸鎬?);
    return;
  }
  
  if (!tabStates[tabId]) {
    tabStates[tabId] = {};
  }
  
  tabStates[tabId] = {
    ...tabStates[tabId],
    status: status,
    ...data
  };
  
  // 鏇存柊鍥炬爣
  setIconState(tabId, status);
}

/**
 * 閲嶇疆鏍囩椤电姸鎬?
 */
function resetTabState(tabId) {
  tabStates[tabId] = { status: 'waiting' };
  setIconState(tabId, 'waiting');
}

/**
 * 浼樺寲鍥炵瓟
 * 浣跨敤OpenRouter API澶勭悊鍥炵瓟浼樺寲
 */
async function optimizeAnswer(tabId, answer, prompt, model) {
  if (!answer) {
    debug('娌℃湁鎵惧埌鍙紭鍖栫殑鍥炵瓟');
    throw new Error('娌℃湁鎵惧埌鍙紭鍖栫殑鍥炵瓟');
  }
  
  debug('寮€濮嬩紭鍖栧洖绛?, { 
    model, 
    answerLength: answer.length,
    promptLength: prompt.length,
    tabId: tabId
  });
  
  updateTabState(tabId, 'processing');
  
  try {
    // 鑾峰彇API瀵嗛挜
    const settings = await chrome.storage.sync.get({
      geminiApiKey: '',
      defaultPrompt: '璇峰杩欐鏂囨湰杩涜鏁寸悊鍜屼紭鍖栵紝浣垮叾鏇村姞娓呮櫚銆佹湁鏉＄悊锛屼繚鎸佸師鎰忕殑鍚屾椂鎻愰珮琛ㄨ揪璐ㄩ噺銆?
    });
    
    debug('宸茶幏鍙朅PI璁剧疆');
    
    // 楠岃瘉API瀵嗛挜
    const apiKey = settings.geminiApiKey;
    if (!apiKey) {
      debug('缂哄皯OpenRouter API瀵嗛挜');
      const error = new Error('缂哄皯OpenRouter API瀵嗛挜锛岃鍦ㄨ缃腑閰嶇疆');
      error.code = 'NO_API_KEY';
      throw error;
    }
    
    // 瀵逛簬鎵€鏈夋ā鍨嬮兘浣跨敤OpenRouter API璋冪敤Gemini 2.5 Pro
    const apiResponse = await processWithOpenRouter(answer, prompt || settings.defaultPrompt, apiKey, model);
    
    // 鏇存柊鐘舵€?
    debug('澶勭悊瀹屾垚锛屾洿鏂扮姸鎬?);
    updateTabState(tabId, 'completed', { 
      result: apiResponse.result,
      apiDetails: apiResponse.details
    });
    
    return apiResponse.result;
  } catch (error) {
    debug('浼樺寲杩囩▼鍑洪敊', error);
    updateTabState(tabId, 'error', { 
      errorMessage: error.message,
      errorCode: error.code || 'UNKNOWN_ERROR',
      errorDetails: error.details || null
    });
    throw error;
  }
}

/**
 * 浣跨敤OpenRouter API璋冪敤Gemini 2.5 Pro澶勭悊鏂囨湰
 */
async function processWithOpenRouter(text, prompt, apiKey, model) { // 如果未指定模型ID，使用默认值 const modelId = model || "google/gemini-2.5-pro-exp-03-25:free";
  debug('浣跨敤OpenRouter API澶勭悊鏂囨湰', { 
    textLength: text.length,
    promptLength: prompt.length 
  });
  
  try {
    // 鏋勫缓瀹屾暣鎻愮ず璇?
    const fullPrompt = `${prompt}\n\n鍘熸枃鍐呭:\n${text}`;
    
    // OpenRouter API 绔偣
    const apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
    
    // 鍑嗗璇锋眰浣?
    const requestBody = {
      model: modelId,
      messages: [
        {
          role: "user",
          content: fullPrompt
        }
      ]
    };
    
    debug('鍙戦€丱penRouter API璇锋眰');
    
    // 璁板綍寮€濮嬫椂闂?
    const startTime = Date.now();
    
    // 鍙戦€佽姹?
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://github.com/yuanbao-assistant',
        'X-Title': 'YuanbaoAssistant'
      },
      body: JSON.stringify(requestBody)
    });
    
    // 璁＄畻璇锋眰鏃堕棿
    const requestTime = Date.now() - startTime;
    
    const responseData = await response.json().catch(err => {
      debug('瑙ｆ瀽API鍝嶅簲JSON鍑洪敊', err);
      const error = new Error('API鍝嶅簲鏍煎紡閿欒: ' + err.message);
      error.code = 'RESPONSE_PARSE_ERROR';
      throw error;
    });
    
    // 璁板綍API璋冪敤璇︽儏锛屼緵璋冭瘯浣跨敤
    const apiCallDetails = {
      requestTime: requestTime,
      statusCode: response.status,
      statusText: response.statusText,
      model: requestBody.model,
      headers: {
        'content-type': response.headers.get('content-type'),
        'x-request-id': response.headers.get('x-request-id')
      }
    };
    
    debug('鏀跺埌OpenRouter API鍝嶅簲', apiCallDetails);
    
    if (!response.ok) {
      debug('OpenRouter API鍝嶅簲閿欒', responseData);
      const errorMessage = responseData.error?.message || 
                          responseData.message || 
                          `API璇锋眰澶辫触: ${response.status} ${response.statusText}`;
      
      const error = new Error(errorMessage);
      error.code = 'API_ERROR';
      error.details = {
        statusCode: response.status,
        responseBody: responseData,
        requestTime: requestTime
      };
      throw error;
    }
    
    // 鎻愬彇鐢熸垚鐨勬枃鏈?
    if (responseData.choices && 
        responseData.choices[0] && 
        responseData.choices[0].message &&
        responseData.choices[0].message.content) {
      
      // 澧炲姞API鍝嶅簲鐨勮缁嗕俊鎭?
      apiCallDetails.tokenUsage = responseData.usage;
      apiCallDetails.modelUsed = responseData.model;
      apiCallDetails.responseTime = Date.now() - startTime;
      
      return {
        result: responseData.choices[0].message.content,
        details: apiCallDetails
      };
    } else {
      debug('API鍝嶅簲鏍煎紡寮傚父', responseData);
      const error = new Error('鏃犳硶浠嶢PI鍝嶅簲涓彁鍙栨枃鏈?);
      error.code = 'INVALID_RESPONSE_FORMAT';
      error.details = responseData;
      throw error;
    }
  } catch (error) {
    debug('OpenRouter API澶勭悊澶辫触', error);
    
    // 澧炲己閿欒淇℃伅
    if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      error.code = 'NETWORK_ERROR';
      error.message = '缃戠粶杩炴帴閿欒: 鏃犳硶杩炴帴鍒癘penRouter API鏈嶅姟鍣?;
    } else if (!error.code) {
      error.code = 'API_PROCESSING_ERROR';
    }
    
    throw error;
  }
}

// 鎵╁睍瀹夎鎴栨洿鏂版椂鐨勫鐞?
chrome.runtime.onInstalled.addListener(function(details) {
  if (details.reason === 'install') {
    debug('鎵╁睍棣栨瀹夎');
    // 鍙互鎵撳紑閫夐」椤垫垨娆㈣繋椤?
  } else if (details.reason === 'update') {
    debug('鎵╁睍宸叉洿鏂?, { oldVersion: details.previousVersion });
  }
});

// 璋冭瘯淇℃伅
debug('鑳屾櫙鑴氭湰鍒濆鍖栧畬鎴?); 






