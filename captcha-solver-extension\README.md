# 🤖 AI验证码破解助手

一个使用 Gemini AI 自动识别和破解 reCAPTCHA 验证码的 Chrome 浏览器扩展。

## ✨ 功能特性

- 🎯 **自动识别** - 智能检测页面中的 reCAPTCHA 验证码
- 🤖 **AI破解** - 使用 Google Gemini AI 识别验证码图像
- 🚀 **一键操作** - 点击按钮即可自动完成整个破解流程
- 📊 **详细状态显示** - 实时显示每个执行步骤和状态信息
- 🔄 **自动模式** - 可设置自动检测和破解验证码
- 💾 **设置保存** - API 密钥和配置自动保存
- 🎨 **美观界面** - 现代化的渐变色设计
- 🔍 **进度追踪** - 可视化进度条和步骤列表

## 📦 安装方法

### 方法一：开发者模式安装

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `captcha-solver-extension` 文件夹
6. 扩展安装完成！

### 方法二：打包安装

1. 在扩展管理页面点击"打包扩展程序"
2. 选择 `captcha-solver-extension` 文件夹
3. 生成 `.crx` 文件
4. 拖拽到 Chrome 扩展页面安装

**注意**: 扩展使用文字图标 🤖，无需额外的图片文件。

## ⚙️ 配置说明

### 1. 获取 Gemini API 密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录你的 Google 账户
3. 点击"Create API Key"创建新密钥
4. 复制生成的 API 密钥

### 2. 配置扩展

1. 点击浏览器工具栏中的扩展图标
2. 在弹出窗口中输入你的 Gemini API 密钥
3. 选择是否启用自动模式
4. 点击"保存设置"

## 🚀 使用方法

### 手动模式

1. 访问包含 reCAPTCHA 的网页
2. 点击扩展图标打开控制面板
3. 点击"🔍 检测验证码"查看是否有验证码
4. 点击"🚀 破解验证码"开始自动破解

### 自动模式

1. 在设置中启用"自动模式"
2. 扩展会自动检测页面中的验证码
3. 发现验证码时会自动进行破解
4. 无需手动操作

## 🔧 工作原理

1. **检测阶段** - 扫描页面中的 reCAPTCHA iframe 元素
2. **交互阶段** - 自动点击"我不是机器人"复选框
3. **截图阶段** - 捕获验证码图像内容
4. **识别阶段** - 调用 Gemini AI API 分析图像
5. **操作阶段** - 根据 AI 结果点击相应图像
6. **提交阶段** - 自动提交验证结果

## 📊 详细执行状态显示

插件提供两种方式显示执行状态：

### 🖥️ 页面进度窗口
- 在页面右上角显示浮动进度窗口
- 实时进度条和百分比显示
- 当前执行步骤和详细状态信息
- 可手动关闭的界面

### 📱 插件弹窗状态
- 在插件弹窗中显示详细步骤列表
- 8个执行步骤的可视化状态
- 实时更新的图标和进度信息
- 错误、警告、成功状态提示

### 🔍 具体执行步骤

1. **🔍 检测reCAPTCHA验证码框架** - 正在扫描页面中的验证码元素...
2. **🖱️ 点击"我不是机器人"复选框** - 正在定位并点击reCAPTCHA复选框...
3. **⏳ 等待图像验证码网格加载** - 等待reCAPTCHA显示图像选择界面...
4. **📸 截取验证码图像区域** - 正在获取验证码图像的屏幕截图...
5. **🤖 AI分析图像并获取位置** - 正在调用Gemini AI识别验证码图像...
6. **🎯 开始点击目标图像** - 准备点击位置 [x, y, z] 的图像...
7. **📤 提交验证结果** - 正在提交选择的图像给reCAPTCHA验证...
8. **✅ 检查验证是否成功** - 等待reCAPTCHA验证结果...

## 📁 文件结构

```
captcha-solver-extension/
├── manifest.json          # 扩展配置文件
├── popup.html             # 弹窗界面
├── popup.js               # 弹窗逻辑
├── content.js             # 内容脚本
├── background.js          # 后台脚本
├── injected.js            # 注入脚本
└── README.md              # 说明文档
```

**注意**: 扩展使用 🤖 emoji 作为图标，无需图片文件。

## 🎨 界面预览

- **渐变色背景** - 紫蓝色渐变设计
- **毛玻璃效果** - 现代化的半透明效果
- **响应式按钮** - 悬停动画和状态反馈
- **状态指示器** - 实时显示操作状态

## ⚠️ 注意事项

1. **API 密钥安全** - 请妥善保管你的 Gemini API 密钥
2. **使用限制** - 遵守 Google API 的使用条款和限制
3. **成功率** - AI 识别准确率取决于验证码复杂度
4. **网络要求** - 需要稳定的网络连接访问 Google API
5. **合法使用** - 仅用于合法目的，遵守网站服务条款

## 🔍 故障排除

### 常见问题

**Q: 扩展无法加载？**
A: 确保开启了开发者模式，并且文件夹结构正确。

**Q: API 调用失败？**
A: 检查 API 密钥是否正确，网络是否能访问 Google 服务。

**Q: 验证码识别失败？**
A: 可能是验证码过于复杂，或者网络延迟导致截图失败。

**Q: 点击无效？**
A: 某些网站可能有额外的反自动化措施，需要调整策略。

### 调试方法

1. 打开浏览器开发者工具 (F12)
2. 查看 Console 标签页的错误信息
3. 检查 Network 标签页的 API 请求状态
4. 在扩展管理页面查看扩展错误

## 📄 许可证

本项目仅供学习和研究使用。使用时请遵守相关法律法规和网站服务条款。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**免责声明**: 本扩展仅用于技术研究和学习目的。使用者需要自行承担使用风险，并遵守相关法律法规。
