<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>MarkDownload Options</title>
    <link rel="stylesheet" type="text/css" href="options.css" media="screen">
</head>

<body>
    <details>
        <summary>
            <h2>Custom text ℹ️</h2>
        </summary>
        <small class="instructions">For the title, as well as the front- and back-matter custom text, you can use the following text replacement values.
            Please note that not all websites will provide all values
            <ul>
                <li><code>{title}</code> - Article Title</li>
                <li><code>{pageTitle}</code> - Title of the actual page</li>
                <li><code>{length}</code> - Length of the article, in characters</li>
                <li><code>{excerpt}</code> - Article description or short excerpt from the content</li>
                <li><code>{byline}</code> - Author metadata</li>
                <li><code>{dir}</code> - Content direction</li>
                <li><code>{date:FORMAT}</code> - The current date and time. Check the <a href="https://momentjs.com/docs/#/displaying/format/" target="_blank" rel="noopener noreferrer">format reference</a> </li>
                <li><code>{keywords}</code> - Meta keywords (if present). Comma separated by default.</li>
                <li><code>{keywords:SEPARATOR}</code> - Meta keywords (if present) separated by SEPARATOR. For example, to separate by space, use <code>{keywords: }</code></li>
            </ul>
            There is also support for all meta tags not mentioned above, should the page you are clipping support them.
            For example, try <code>{og:image}</code> or any other widely supported meta tags
            <p>
                URL information:
                <ul>
                    <li><code>{baseURI}</code> - The url of the article</li>
                    <li><code>{origin}</code> - The origin of the URL, that is its scheme, its domain and its port.</li>
                    <li><code>{host}</code> - The domain (that is the <em>hostname</em>) followed by (if a port was specified) a <code>':'</code> and the <em>port</em> of the URL.</li>
                    <li><code>{hostname}</code> - The domain of the URL.</li>
                    <li><code>{port}</code> - The port number of the URL.</li>
                    <li><code>{protocol}</code> - The protocol scheme of the URL, including the final <code>':'</code>.</li>
                    <li><code>{pathname}</code> - An initial <code>'/'</code> followed by the path of the URL, not including the query string or fragment.</li>
                    <li><code>{search}</code> - The URL's parameter string; if any parameters are provided, this string includes all of them, beginning with the leading <code>?</code> character.</li>
                    <li><code>{hash}</code> - A '#' followed by the fragment identifier of the URL.</li>
                </ul>
            </p>
            <p>
                Additionally, you can 'parameterize' any of the text variables (other than date and keywords) by using the following syntax:
                <ul>
                    <li><code>{variable:pascal}</code> - PascalCase: Every word capital</li>
                    <li><code>{variable:camel}</code> - camelCase: every word capital except the first word</li>
                    <li><code>{variable:kebab}</code> - kebab-case: hyphens between words, all lowercase</li>
                    <li><code>{variable:snake}</code> - snake_case: underscores between words, all lowercase</li>
                </ul>
            </p>
        </small>
    </details>

    <h3>Title template</h3>
    <div class="textbox-container">
        <label for="title">Template for title/filename</label>
        <input type="text" id="title" name="title" role="textbox" />
    </div>

    <br />

    <div class="textbox-container" id="mdClipsFolder">
        <label for="mdClipsFolder">Folder inside <code>Downloads/</code> to store MarkDownload clips:</label>
        <input type="text" name="mdClipsFolder" role="textbox" />
    </div>

    <br />

    <div class="textbox-container">
        <label for="disallowedChars">Disallowed Characters (to strip out of title/filename —
            in addition to <code>/</code>, <code>?</code>, <code>&lt;</code>, <code>&gt;</code>, <code>\</code>,
            <code>:</code>, <code>*</code>, <code>|</code>, <code>"</code>)</label>
        <input type="text" id="disallowedChars" name="disallowedChars" role="textbox" />
    </div>
    
    <h3>Front-matter template</h3>
    <div class="textbox-container">
        <label for="frontmatter">Enter the text here that should appear at the top of the output file.</label> <br />
        <div class="input-sizer"><textarea name="frontmatter" id="frontmatter" class="textarea" role="textbox"></textarea></div>
    </div>

    <h3>Back-matter template</h3>
    <div class="textbox-container">
        <label for="backmatter">Enter the text here that should appear at the bottom of the output file.</label> <br />
        <div class="input-sizer"><textarea name="backmatter" id="backmatter" class="textarea" role="textbox"></textarea></div>
    </div>

    <div class="checkbox-container">
        <input name="includeTemplate" id="includeTemplate" type="checkbox" />
        <label for="includeTemplate">Append front/back template to clipped text</label>
    </div>

    <hr>

    <div id="otherOptions">
        <h2>Other options</h2>

        <div class="checkbox-container" id="contextMenus-container">
            <input name="contextMenus" id="contextMenus" type="checkbox" />
            <label for="contextMenus">Enable Context Menus</label>
        </div>

        <br />

        <div class="checkbox-container" id="obsidian-container">
            <h3>Obsidian Integration</h3>
            <p>
                For integration with obsidian, you need to install and enable community plugins named <code>Advanced Obsidian URI</code>. This plugin help us to bypass character limitation in URL. Because it's using clipboard as the source for creating new file.
More information:  <a href="https://vinzent03.github.io/obsidian-advanced-uri/">Obsidian Advanced URI</a><br/>
            </p>
            <input name="obsidianIntegration" id="obsidianIntegration" type="checkbox" />
            <label for="obsidianIntegration">Enable Obsidian Integration</label>
        </div>

        <br />

        <div class="textbox-container" id="obsidianVault">
            <label for="obsidianVault">Obsidian Vault Name: </label>
            <input type="text" name="obsidianVault" role="textbox" placeholder="If blank, it will use the main Vault. Please specify it." />
            <label for="obsidianFolder">Obsidian Folder Name: </label>
            <input type="text" name="obsidianFolder" role="textbox" placeholder="Enter Folder Name eg. Clippers"/>
        </div>

        <br />

        <div class="radio-container" id="downloadMode">
            <h3>Download Mode</h3>
            <p>
                Method to use for downloading Markdown files. Set to "Content Link" if you're having trouble with the Downloads API
                (Sometimes conflicts can occur with other download extensions, leading to randomly generated filenames and other symptoms). <br/>
            </p>
            <p><strong>Note:</strong> "Content Link" mode disables some functionality such as downloading images or using subfolders in the filename.</p>
            <input type="radio" name="downloadMode" id="downloadsApi" value="downloadsApi" />
            <label for="downloadsApi">Downloads API (recommended)</label>
            <input type="radio" name="downloadMode" id="contentLink" value="contentLink" />
            <label for="contentLink">Content Link</label>
            <!-- <input type="radio" name="downloadMode" id="obsidianUri" value="obsidianUri" />
            <label for="obsidianUri">Obsidian URI (obsidian://new)</label> -->
        </div>
        <!-- <div id="obsidianUriGroup">
            <div class="textbox-container" id="obsidianVault">
                <label for="obsidianVault">Obsidian vault name: </label>
                <input type="text" name="obsidianVault" role="textbox" />
            </div>
            <div class="radio-container" id="obsidianPathType">
                <h3>Obsidian Path Type</h3>
                <input type="radio" name="obsidianPathType" id="name" value="name" />
                <label for="name"><span>Relative to default note folder</span>
                </label>
                <input type="radio" name="obsidianPathType" id="file" value="file" />
                <label for="file"><span>Relative to vault root</span>
                    <small>Requires Obsidian 0.11.1 or higher</small>
                </label>
            </div>
        </div> -->
        <div id="downloadModeGroup">
            <div class="checkbox-container" id="saveAs-container">
                <input name="saveAs" id="saveAs" type="checkbox" />
                <label for="saveAs">Always show Save As dialog (regardless of browser setting)</label>
            </div>

            <br />

            <div class="checkbox-container" id="downloadImages-container">
                <input name="downloadImages" id="downloadImages" type="checkbox" />
                <label for="downloadImages">Download images alongside markdown files</label>
            </div>

            <div class="textbox-container" id="imagePrefix">
                <label for="imagePrefix">Image filename prefix template: </label>
                <input type="text" name="imagePrefix" role="textbox" />
            </div>
        </div>
    </div>

    <hr>

    <h2>Markdown conversion options</h2>

    <div class="radio-container" id="headingStyle">
        <h3>Heading Style</h3>
        <input type="radio" name="headingStyle" id="setext" value="setext" />
        <label for="setext"><span>Setext-Style Headers</span>
            <pre>All About Dogs
==============</pre>
        </label>
        <input type="radio" name="headingStyle" id="atx" value="atx" />
        <label for="atx"><span>Atx-Style Headers</span>
            <pre># All About Dogs</pre>
        </label>
    </div>

    <div class="radio-container" id="hr">
        <h3>Horizontal Rule style</h3>
        <input type="radio" name="hr" id="***" value="***" />
        <label for="***"><code>***</code></label>
        <input type="radio" name="hr" id="---" value="---" />
        <label for="---"><code>---</code></label>
        <input type="radio" name="hr" id="___" value="___" />
        <label for="___"><code>___</code></label>
    </div>

    <div class="radio-container" id="bulletListMarker">
        <h3>Bullet List Marker</h3>
        <input type="radio" name="bulletListMarker" id="*" value="*" />
        <label for="*"><code>*</code></label>
        <input type="radio" name="bulletListMarker" id="-" value="-" />
        <label for="-"><code>-</code></label>
        <input type="radio" name="bulletListMarker" id="+" value="+" />
        <label for="+"><code>+</code></label>
    </div>

    <div class="radio-container" id="codeBlockStyle">
        <h3>Code Block Style</h3>
        <input type="radio" name="codeBlockStyle" id="indented" value="indented" />
        <label for="indented">Indented
            <pre>····const helloWorld = () => {
········console.log("Hello World");
····}</pre>
        </label>
        <input type="radio" name="codeBlockStyle" id="fenced" value="fenced" />
        <label for="fenced">Fenced
            <pre>```
const helloWorld = () => {
····console.log("Hello World");
}
```</pre>
        </label>
    </div>

    <div class="radio-container" id="fence">
        <h3>Code Block Fence</h3>
        <input type="radio" name="fence" id="```" value="```" />
        <label for="```"><code>```</code></label>
        <input type="radio" name="fence" id="~~~" value="~~~" />
        <label for="~~~"><code>~~~</code></label>
    </div>

    <div class="radio-container" id="emDelimiter">
        <h3>Emphasis (italics) Delimiter</h3>
        <input type="radio" name="emDelimiter" id="em_" value="_" />
        <label for="em_"><code>_italics_</code></label>
        <input type="radio" name="emDelimiter" id="em*" value="*" />
        <label for="em*"><code>*italics*</code></label>
        <input type="radio" name="emDelimiter" id="em__" value="__" />
        <label for="em__"><code>__italics__</code> <strong>(Non-standard. Roam specific.)</strong></label>
    </div>

    <div class="radio-container" id="strongDelimiter">
        <h3>Strong (bold) Delimiter</h3>
        <input type="radio" name="strongDelimiter" id="**" value="**" />
        <label for="**"><code>**bold**</code></label>
        <input type="radio" name="strongDelimiter" id="__" value="__" />
        <label for="__"><code>__bold__</code></label>
    </div>

    <div class="radio-container" id="linkStyle">
        <h3>Link Style</h3>
        <input type="radio" name="linkStyle" id="inlined" value="inlined" />
        <label for="inlined">Inlined
            <pre>[Google](http://google.com)</pre>
        </label>
        <input type="radio" name="linkStyle" id="referenced" value="referenced" />
        <label for="referenced">Referenced
            <pre>[Google]

[Google]: http://google.com</pre>
        </label>
        <input type="radio" name="linkStyle" id="stripLinks" value="stripLinks" />
        <label for="stripLinks">Strip Links
            <pre>Google</pre>
        </label>
    </div>

    <div class="radio-container" id="linkReferenceStyle">
        <h3>Link Reference Style</h3>
        <input type="radio" name="linkReferenceStyle" id="full" value="full"  />
        <label for="full">Full
            <pre>[Google][1]

[1]: http://google.com</pre>
        </label>
        <input type="radio" name="linkReferenceStyle" id="collapsed" value="collapsed" />
        <label for="collapsed">Collapsed
            <pre>[Google][]

[Google]: http://google.com</pre>
        </label>
        <input type="radio" name="linkReferenceStyle" id="shortcut" value="shortcut" />
        <label for="shortcut">Shortcut
            <pre>[Google]

[Google]: http://google.com</pre>
        </label>
    </div>
    
    <div class="radio-container" id="imageOptions">
        <h3>Image Style</h3>
        <input type="radio" name="imageStyle" id="originalSource" value="originalSource" />
        <label for="originalSource">Original Source
            <pre>![](http://example.com/img/image.jpg)</pre>
        </label>
        <input type="radio" name="imageStyle" id="noImage" value="noImage" />
        <label for="noImage">Strip Images
            <pre> &nbsp; &nbsp; &nbsp; </pre>
        </label>
        <p><strong>Note:</strong> The following only apply if Download Images is on.</p>
        <input type="radio" name="imageStyle" id="markdown" value="markdown" />
        <label for="markdown">Pure Markdown
            <pre>![](folder/image.jpg)</pre>
        </label>
        <input type="radio" name="imageStyle" id="base64" value="base64" />
        <label for="base64">Base64 encoded
            <pre>![](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==)</pre>
        </label>
        <input type="radio" name="imageStyle" id="obsidian" value="obsidian" />
        <label for="obsidian">Obsidian internal embed
            <pre>![[folder/image.jpg]]</pre>
        </label>
        <input type="radio" name="imageStyle" id="obsidian-nofolder" value="obsidian-nofolder" />
        <label for="obsidian-nofolder">Obsidian internal embed (no folder prefix)
            <pre>![[image.jpg]]</pre>
        </label>
    </div>

    <div class="radio-container" id="imageRefOptions">
        <h3>Image Reference Style</h3>
        <p><strong>Note:</strong> The following only apply if Image Style is markdown (not Obsidian-styled).</p>
        <input type="radio" name="imageRefStyle" id="img-inlined" value="inlined" />
        <label for="img-inlined">Inlined
            <pre>![](address/of/image.jpg)</pre>
        </label>
        <input type="radio" name="imageRefStyle" id="img-referenced" value="referenced" />
        <label for="img-referenced">Referenced
            <pre> ![][fig1]

[fig1]: address/of/image.jpg</pre>
        </label>
    </div>

    <div class="checkbox-container" id="turndownEscape-container">
        <input name="turndownEscape" id="turndownEscape" type="checkbox" />
        <label for="turndownEscape">Escape Markdown Characters
            <p><small>
                By default, backslashes (<code>\</code>) are used to escape 
                Markdown characters in the HTML input. This ensures that these
                characters are not interpreted as Markdown. For example, the
                contents of <code>&lt;h1&gt;1. Hello world&lt;/h1&gt;</code>
                needs to be escaped to <code>1\. Hello world</code>, otherwise
                it will be interpreted as a list item rather than a heading.
                <br />Disabling this option disables this escaping.
            </small></p>
        </label>
    </div>

    <hr>
    
    <h2>Import / Export</h2>

    <div class="button-container">
        <label for="import">Import from a previous backup</label> <br />
        <div class="input-sizer"><button name="import" id="import"> Choose file...<input type="file" name="import-file" id="import-file" /></button> </div>

        <label for="export">Export current options</label> <br />
        <div class="input-sizer"><button name="export" id="export">Export</button></div>
    </div>

    <div id="spinner" style="display: none;"></div>
    <div id="status" class="status"></div>

    <script src="../browser-polyfill.min.js"></script>
    <script src="../shared/default-options.js"></script>
    <script src="../shared/context-menus.js"></script>
    <script src="options.js"></script>
</body>

</html>
