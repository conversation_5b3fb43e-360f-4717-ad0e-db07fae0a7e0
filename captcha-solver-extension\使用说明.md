# 🤖 AI验证码破解助手 - 详细使用说明

## 🎯 新功能特色

### ✨ 透明化进度显示
- **实时步骤反馈** - 每个操作步骤都有详细显示
- **可视化进度条** - 直观显示破解进度
- **详细状态信息** - 每步操作的具体结果
- **错误诊断** - 失败时提供详细错误信息

### 📊 8步破解流程

1. **🔍 步骤1: 检测验证码** - 扫描页面中的reCAPTCHA元素
2. **🖱️ 步骤2: 点击复选框** - 自动点击"我不是机器人"
3. **⏳ 步骤3: 等待加载** - 等待图像验证码网格加载
4. **📸 步骤4: 截取图像** - 精确截取验证码图像区域
5. **🤖 步骤5: AI分析** - 使用Gemini AI识别目标图像
6. **🎯 步骤6: 点击图像** - 根据AI结果点击相应图像
7. **📤 步骤7: 提交验证** - 自动提交验证结果
8. **🔍 步骤8: 验证结果** - 检查验证是否成功

## 🚀 安装和配置

### 1. 安装插件
```bash
1. 打开 Chrome 浏览器
2. 访问 chrome://extensions/
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 captcha-solver-extension 文件夹
```

### 2. 配置API密钥
```bash
1. 获取 Gemini API 密钥: https://makersuite.google.com/app/apikey
2. 点击浏览器工具栏的插件图标 🤖
3. 在弹窗中输入API密钥
4. 点击"保存设置"
```

## 🎮 使用方法

### 手动模式
1. 访问包含验证码的网页
2. 点击插件图标打开控制面板
3. 点击"🚀 破解验证码"按钮
4. 观察右上角的进度显示器
5. 等待破解完成

### 自动模式
1. 在插件设置中启用"自动模式"
2. 插件会自动检测验证码并破解
3. 无需手动操作

## 📱 进度显示器说明

### 界面元素
- **标题栏** - 显示"🤖 AI验证码破解进度"
- **当前步骤** - 显示正在执行的步骤
- **详细信息** - 显示步骤的具体操作内容
- **进度条** - 可视化显示完成百分比
- **关闭按钮** - 可手动关闭进度显示器

### 状态颜色
- 🔵 **蓝色** - 正在执行的步骤
- 🟢 **绿色** - 成功完成的步骤
- 🔴 **红色** - 失败的步骤
- 🟡 **黄色** - 警告信息

## 🔧 调试和故障排除

### 查看详细日志
1. 按F12打开开发者工具
2. 切换到Console标签
3. 查看详细的操作日志

### 常见问题

**Q: 进度显示器不出现？**
A: 检查插件是否正确加载，刷新页面重试

**Q: 步骤4截图失败？**
A: 确保给予了必要的权限，检查页面是否完全加载

**Q: 步骤5 AI识别失败？**
A: 检查API密钥是否正确，网络是否能访问Google服务

**Q: 步骤6点击无效？**
A: 可能遇到反自动化检测，尝试刷新页面重试

**Q: 验证码一直出现？**
A: 可能触发了安全机制，建议：
   - 等待一段时间再尝试
   - 清除浏览器缓存
   - 更换网络环境

### 日志示例
```
🔍 步骤1: 检测验证码...
✅ 步骤1: 验证码检测成功
🖱️ 步骤2: 点击"我不是机器人"复选框...
✅ 步骤2: 复选框点击成功
📸 步骤4: 截取验证码图像...
📐 图像框架尺寸: 300x300
✅ 步骤4: 验证码图像截取成功
🤖 步骤5: AI正在分析图像...
📊 图像数据大小: 45KB
✅ 步骤5: AI识别完成，找到 3 个目标图像: [1, 4, 7]
🎯 步骤6: 点击识别出的图像...
✅ 步骤6: 成功点击 3 个图像
🎉 验证码破解成功！
```

## ⚙️ 高级设置

### 自定义配置
- **自动模式** - 启用后自动检测和破解验证码
- **调试模式** - 显示更详细的调试信息
- **延迟设置** - 调整操作间的延迟时间

### 性能优化
- 插件会自动优化图像大小以提高识别速度
- 智能检测网格布局（3x3或4x4）
- 自适应点击策略

## 🛡️ 安全和隐私

- API密钥仅存储在本地浏览器中
- 图像数据仅用于验证码识别
- 不收集任何个人信息
- 遵守网站服务条款

## 📞 技术支持

如遇到问题，请提供：
1. 浏览器版本信息
2. 插件版本号
3. 详细的错误日志
4. 问题复现步骤

---

**免责声明**: 本插件仅用于技术研究和学习目的。使用者需要自行承担使用风险，并遵守相关法律法规和网站服务条款。
