# 文件快速搬运工具 v1.0

## 🎯 功能特性
- **双模式操作**：复制模式（保留原文件）+ 搬运模式（移动文件）
- **智能文件扫描**：自动检测新文件，高亮显示
- **批量选择**：复选框界面，支持全选/取消全选
- **路径记忆**：自动保存常用路径，无需重复设置
- **一键操作**：选择文件后一键执行搬运/复制
- **便携设计**：可放置在任意位置使用

## 📁 文件结构
```
搬运工具/
├── file_mover.py              # 主程序
├── 启动搬运工具.bat           # 启动脚本
├── README_文件快速搬运工具.md # 使用说明
├── mover_settings.json        # 路径设置（自动生成）
└── file_cache.json           # 文件缓存（自动生成）
```

## 🚀 快速开始

### 1. 选择操作模式
- **📋 复制模式**：保留原文件，在目标位置创建副本
- **✂️ 搬运模式**：移动文件，原位置文件将被删除

### 2. 设置路径
- **源文件夹**：选择要扫描的文件夹
- **目标文件夹**：选择文件的目标位置
- 路径会自动保存，下次启动时自动加载

### 3. 扫描文件
- 点击"刷新扫描"或选择源文件夹后自动扫描
- 新文件会标记为"新"状态
- 显示文件类型、大小、修改时间等信息

### 4. 选择文件
- 双击文件行切换选择状态
- 使用"全选"/"取消全选"批量操作
- ☑ 表示已选择，☐ 表示未选择

### 5. 执行操作
- 点击"🚀 执行操作"按钮
- 系统会根据选择的模式执行复制或搬运
- 显示操作进度和结果

## 🔧 界面说明

### 操作模式区域
```
┌─────────────────────────────────────────┐
│ ○ 📋 复制模式 (保留原文件)              │
│ ● ✂️ 搬运模式 (移动文件)               │
└─────────────────────────────────────────┘
```

### 路径设置区域
```
源文件夹: [路径输入框] [选择文件夹] [刷新扫描]
目标文件夹: [路径输入框] [选择文件夹]
```

### 文件列表区域
```
┌─────────────────────────────────────────────────────────┐
│ 选择 │ 文件名        │ 类型 │ 大小  │ 修改时间    │ 状态 │
├─────────────────────────────────────────────────────────┤
│ ☑   │ 新文档.txt    │ 文件 │ 1.2KB │ 2025-07-12  │ 新   │
│ ☐   │ 图片文件夹    │ 文件夹│ 4.5MB │ 2025-07-11  │      │
│ ☑   │ 报告.pdf      │ 文件 │ 2.1MB │ 2025-07-10  │      │
└─────────────────────────────────────────────────────────┘
```

### 操作按钮区域
```
[全选] [取消全选] [🚀 执行操作] [💾 保存设置]
```

## ⚙️ 配置文件

### mover_settings.json
自动保存的设置文件：
```json
{
  "source_folder": "C:\\Users\\<USER>\\Documents\\源文件夹",
  "target_folder": "D:\\目标文件夹",
  "operation_mode": "copy"
}
```

### file_cache.json
用于检测新文件的缓存：
```json
{
  "C:\\path\\to\\file.txt": "2025-07-12 10:30",
  "C:\\path\\to\\folder": "2025-07-11 15:45"
}
```

## 🛡️ 安全特性

- **操作确认**：执行前显示详细确认对话框
- **文件冲突处理**：目标文件存在时询问是否覆盖
- **错误处理**：详细的错误信息和部分成功提示
- **操作记录**：状态栏显示操作结果
- **路径验证**：自动检查路径有效性

## 📋 使用场景

### 1. 文件整理
- 从下载文件夹整理文件到分类文件夹
- 清理桌面文件到对应目录

### 2. 项目管理
- 将新项目文件复制到工作目录
- 备份重要文件到安全位置

### 3. 批量处理
- 选择多个文件进行批量移动
- 快速复制常用文件到多个位置

### 4. 新文件监控
- 定期扫描特定文件夹的新增文件
- 快速处理新下载的文件

## 💡 使用技巧

1. **路径记忆**：设置好常用路径后会自动保存，提高效率
2. **新文件检测**：定期刷新扫描可以快速找到新增文件
3. **批量操作**：使用全选功能可以快速处理大量文件
4. **模式切换**：根据需要灵活切换复制/搬运模式
5. **便携使用**：整个工具文件夹可以复制到U盘随身携带

## 🔄 操作流程

```
选择模式 → 设置路径 → 扫描文件 → 选择文件 → 执行操作
    ↓         ↓         ↓         ↓         ↓
  复制/搬运   自动保存   新文件检测   批量选择   结果反馈
```

## 📝 更新日志

### v1.0 (2025-07-12)
- ✅ 双模式操作（复制/搬运）
- ✅ 智能新文件检测
- ✅ 路径自动保存
- ✅ 批量文件选择
- ✅ 一键执行操作
- ✅ 便携式设计
- ✅ 友好的用户界面

## ❓ 常见问题

**Q: 复制模式和搬运模式有什么区别？**
A: 复制模式保留原文件，搬运模式会删除原文件（相当于剪切）。

**Q: 路径设置会保存吗？**
A: 是的，选择路径后会自动保存到settings文件中。

**Q: 如何识别新文件？**
A: 工具会记录已扫描的文件，新增的文件会标记为"新"。

**Q: 可以处理文件夹吗？**
A: 可以，工具支持文件和文件夹的批量操作。

**Q: 如果目标位置已有同名文件怎么办？**
A: 系统会询问是否覆盖，您可以选择跳过或覆盖。

## 📦 系统要求

- Python 3.6+
- tkinter（通常随Python安装）
- Windows/Linux/macOS

## 🚀 启动方式

### Windows
双击 `启动搬运工具.bat`

### 其他系统
```bash
python file_mover.py
```
