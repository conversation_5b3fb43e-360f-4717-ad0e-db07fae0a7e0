/**
 * 元宝助手 - 内容样式
 * 定义插入到页面中的元素的样式
 */

/* 优化回答容器 */
.yuanbao-optimized-answer {
  border: 2px solid #4CAF50 !important;
  border-radius: 8px !important;
  padding: 16px !important;
  margin: 20px 0 !important;
  background-color: #f9fff9 !important;
  position: relative !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif !important;
  color: #333 !important;
  line-height: 1.6 !important;
  font-size: 15px !important;
  max-width: 100% !important;
  overflow: auto !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.1) !important;
}

.yuanbao-optimized-answer:hover {
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2) !important;
}

/* 标题栏 */
.yuanbao-optimized-header {
  color: #4CAF50 !important;
  font-weight: bold !important;
  margin-bottom: 12px !important;
  padding-bottom: 10px !important;
  border-bottom: 1px solid rgba(76, 175, 80, 0.2) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.yuanbao-optimized-title {
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.yuanbao-optimized-close {
  background: none !important;
  border: none !important;
  color: #666 !important;
  cursor: pointer !important;
  font-size: 16px !important;
  padding: 0 !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  transition: background-color 0.2s !important;
}

.yuanbao-optimized-close:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

/* 内容区 */
.yuanbao-optimized-content {
  white-space: pre-wrap !important;
  word-break: break-word !important;
}

/* 底部署名 */
.yuanbao-optimized-footer {
  margin-top: 15px !important;
  font-size: 12px !important;
  color: #666 !important;
  text-align: right !important;
  opacity: 0.8 !important;
  padding-top: 10px !important;
  border-top: 1px solid rgba(76, 175, 80, 0.1) !important;
}

/* 动画效果 */
@keyframes yuanbaoFadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.yuanbao-optimized-answer {
  animation: yuanbaoFadeIn 0.3s ease-out forwards !important;
} 