:root {
    --focus: orange;
    --bg: #fff;/*#0A001F;*/
    --fg: #000;
    --border: #eee;
    --primary: #4A90E2;
    --fg-primary: #eee;
    --bg-primary: #ECF5FF;
    --border-primary: #4A90E2;
    --font-size: 14px;
}

@media(prefers-color-scheme: dark) {
    :root {
        --focus: orange;
        --bg: #2A2A2E;
        --fg: #f8f8f8;
        --border: #555;
        --primary: #4A90E2;
        --fg-primary: #eee;
        --bg-primary: hsl(212, 80%, 30%);
        --border-primary: #4A90E2;
        --pre-bg: #474749;
    }
    .cm-s-xq-dark.CodeMirror {
        background-color: var(--bg);
    }
}

/* Works on Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--pre-bg) var(--bg);
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
    width: 12px;
}

*::-webkit-scrollbar-track {
    background: var(--bg);
}

*::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 20px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 350px;
    height: 500px;
    border-radius: 0.5em;
    overflow: hidden;
    font-family: sans-serif;
    background: var(--bg);
    font-size: var(--font-size);
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}
* {
    box-sizing: border-box;
}

*:focus {
    border: 3px dotted var(--focus-color) !important;
}

#spinner:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    border-radius: 50%;
    border-top: 2px solid var(--primary);
    border-right: 2px solid transparent;
    animation: spinner .6s linear infinite;
}

.CodeMirror {
    border: none;
    padding: 0.5em;
    flex: 1;
}

input#title {
    padding: 0.5em;
    color: var(--fg);
    background-color: var(--bg);
    border: 1px solid var(--border);
    font-size: 1.01em;
}

#container {
    width: 100%;
    height: 100%;
    display: none;
    flex-direction: column;
}
.row {
    flex-direction: row;
    display:flex;
}

a.button {
    display: block;
    padding: 0.5em 0;
    background-color: var(--primary);
    color: var(--fg-primary);
    text-align: center;
    text-decoration: none;
    line-height: 1em;
    border: 1px solid var(--bg);
    border-collapse: collapse;
}

#options {
    display: block;
    position: absolute;
    top:2px;
    right:2px;
    font-size: 0;
    text-decoration: none;
    background-color: var(--primary);
    color: var(--fg-primary);
    border-radius: 2rem;
    width: 2rem;
    height: 2rem;
    text-align: center;
    line-height: 2rem;
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", sans-serif;
}
#options::before {
    content: '⚙';
    font-size: 1rem;
}

#downloadSelection {
    background-color: var(--bg-primary);
    display: none;
}

#selected, #document {
    flex:1;
}

#clipOption {
    display:none;
}



.button.toggle {
    background: var(--bg);
    border: 1px solid var(--border);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    color:var(--fg);
    cursor: pointer;
    padding: 10px 10px 10px 30px;
    display: inline-block;
    font-weight: 600;
    transition: .3s ease all;
}

.button.toggle:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.button.toggle.checked {
    background: var(--bg-primary);
    border-color: var(--border-primary);
}

.check {
    background: var(--bg-primary);
    border-radius: 50%;
    content:'';
    height: 20px;
    left: 10px;
    position: absolute;
    top: calc(50% - 10px);
    transition: .3s ease background-color;
    width: 20px;
}

.checked .check {
    background-color: var(--primary);
    background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48c3ZnIHdpZHRoPSIyNiIgaGVpZ2h0PSIyMCIgdmVyc2lvbj0iMS4xIiB2aWV3Qm94PSIyLjAyOTY4IC00MC4wOTAzIDI2IDIwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj48IS0tR2VuZXJhdGVkIGJ5IElKU1ZHIChodHRwczovL2dpdGh1Yi5jb20vaWNvbmphci9JSlNWRyktLT48cGF0aCBkPSJNMjcuOTc0MywtMzYuMTI3MmMwLDAuNDQ2NDI4IC0wLjE1NjI1LDAuODI1ODkzIC0wLjQ2ODc1LDEuMTM4MzlsLTEyLjEyMDUsMTIuMTIwNWwtMi4yNzY3OSwyLjI3Njc5Yy0wLjMxMjUsMC4zMTI1IC0wLjY5MTk2NCwwLjQ2ODc1IC0xLjEzODM5LDAuNDY4NzVjLTAuNDQ2NDI4LDAgLTAuODI1ODkzLC0wLjE1NjI1IC0xLjEzODM5LC0wLjQ2ODc1bC0yLjI3Njc5LC0yLjI3Njc5bC02LjA2MDI3LC02LjA2MDI3Yy0wLjMxMjUsLTAuMzEyNSAtMC40Njg3NSwtMC42OTE5NjUgLTAuNDY4NzUsLTEuMTM4MzljMCwtMC40NDY0MjkgMC4xNTYyNSwtMC44MjU4OTMgMC40Njg3NSwtMS4xMzgzOWwyLjI3Njc5LC0yLjI3Njc5YzAuMzEyNSwtMC4zMTI1IDAuNjkxOTY1LC0wLjQ2ODc1IDEuMTM4MzksLTAuNDY4NzVjMC40NDY0MjksMCAwLjgyNTg5MywwLjE1NjI1IDEuMTM4MzksMC40Njg3NWw0LjkyMTg4LDQuOTM4NjJsMTAuOTgyMSwtMTAuOTk4OWMwLjMxMjUsLTAuMzEyNSAwLjY5MTk2NCwtMC40Njg3NSAxLjEzODM5LC0wLjQ2ODc1YzAuNDQ2NDI4LDAgMC44MjU4OTMsMC4xNTYyNSAxLjEzODM5LDAuNDY4NzVsMi4yNzY3OCwyLjI3Njc5YzAuMzEyNSwwLjMxMjUgMC40Njg3NSwwLjY5MTk2NCAwLjQ2ODc1LDEuMTM4MzlaIiB0cmFuc2Zvcm09InNjYWxlKDEuMDAxOTgpIiBmaWxsPSIjZmZmIj48L3BhdGg+PC9zdmc+');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px;
}
