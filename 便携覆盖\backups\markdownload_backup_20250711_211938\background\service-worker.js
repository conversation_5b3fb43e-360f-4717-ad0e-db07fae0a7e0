// Service Worker for Manifest V3
// Import all the necessary scripts using importScripts

try {
  // Import all required libraries and scripts
  importScripts(
    '/browser-polyfill.min.js',
    '/background/apache-mime-types.js',
    '/background/moment.min.js',
    '/background/turndown.js',
    '/background/turndown-plugin-gfm.js',
    '/background/Readability.js',
    '/shared/context-menus.js',
    '/shared/default-options.js',
    '/background/background.js'
  );

  console.log('MarkDownload service worker loaded successfully');
} catch (error) {
  console.error('Error loading MarkDownload service worker:', error);
}
