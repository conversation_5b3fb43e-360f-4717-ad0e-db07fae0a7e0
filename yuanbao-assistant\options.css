/**
 * 元宝助手 - 设置页面样式
 */

/* 基础样式 */
:root {
  --primary-color: #4CAF50;
  --primary-dark: #388E3C;
  --primary-light: #81C784;
  --primary-bg: #E8F5E9;
  --accent-color: #1B5E20;
  --text-color: #212121;
  --text-light: #757575;
  --white: #FFFFFF;
  --background-color: #f5f5f5;
  --card-background: #fff;
  --border-color: #DDDDDD;
  --error-color: #f44336;
  --success-color: #4CAF50;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-color);
  background-color: var(--primary-bg);
  line-height: 1.6;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 30px;
  border: 1px solid var(--primary-light);
}

/* 头部样式 */
header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--primary-light);
}

/* 文字logo样式 */
.text-logo {
  width: 60px;
  height: 60px;
  margin: 0 auto 16px;
  background-color: var(--primary-color);
  color: white;
  font-weight: bold;
  font-size: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

h1 {
  font-size: 28px;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.version {
  font-size: 16px;
  margin-left: 6px;
  color: var(--primary-color);
  font-weight: normal;
  vertical-align: top;
}

/* 卡片样式 */
.card {
  background-color: var(--card-background);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid var(--primary-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.description {
  margin-bottom: 20px;
  color: var(--text-light);
  font-size: 14px;
}

/* 表单元素 */
.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.input-container {
  display: flex;
  position: relative;
}

.api-key-input, .select-input, .textarea-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
  color: var(--text-color);
  transition: border-color 0.3s;
}

.api-key-input {
  padding-right: 40px;
}

.api-key-input:focus, .select-input:focus, .textarea-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

.toggle-visibility {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-light);
  font-size: 16px;
}

.toggle-visibility:hover {
  color: var(--primary-color);
}

/* API测试相关样式 */
.api-test-container {
  margin-top: 15px;
}

.api-test-result {
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
  font-size: 13px;
  white-space: pre-wrap;
  max-height: 150px;
  overflow-y: auto;
}

.api-test-result.success {
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.2);
  color: var(--success-color);
}

.api-test-result.error {
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.2);
  color: var(--error-color);
}

.api-test-result.loading {
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.2);
  color: #2196F3;
}

.help-text {
  margin-top: 5px;
  font-size: 12px;
  color: var(--text-light);
}

.help-text a {
  color: var(--primary-color);
  text-decoration: none;
}

.help-text a:hover {
  text-decoration: underline;
}

.optional {
  color: var(--text-light);
  font-weight: normal;
  font-size: 13px;
}

/* 复选框样式 */
.checkbox-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.checkbox-group label {
  margin-bottom: 0;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary-color);
}

/* 按钮样式 */
.primary-btn {
  background-color: var(--primary-color);
  color: white;
  padding: 12px 18px;
  transition: all 0.3s;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.primary-btn:hover {
  background-color: var(--primary-dark);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.secondary-btn {
  background-color: #e0e0e0;
  color: var(--text-color);
  padding: 10px 16px;
  border-radius: 6px;
  transition: all 0.3s;
}

.secondary-btn:hover {
  background-color: #ccc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 操作区域 */
.actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

/* 状态消息 */
.status-message {
  margin-top: 20px;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s;
}

.status-message.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(76, 175, 80, 0.2);
  opacity: 1;
}

.status-message.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(244, 67, 54, 0.2);
  opacity: 1;
}

/* 页脚 */
footer {
  margin-top: 30px;
  text-align: center;
  font-size: 13px;
  color: var(--text-light);
  padding-top: 15px;
  border-top: 1px solid var(--primary-light);
}

footer a {
  color: var(--primary-color);
  text-decoration: none;
}

footer a:hover {
  text-decoration: underline;
  color: var(--primary-dark);
}

/* 响应式调整 */
@media (max-width: 600px) {
  .container {
    padding: 15px;
  }
  
  .actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .primary-btn, .secondary-btn {
    width: 100%;
  }
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
}

.button-group button {
  flex: 1;
} 