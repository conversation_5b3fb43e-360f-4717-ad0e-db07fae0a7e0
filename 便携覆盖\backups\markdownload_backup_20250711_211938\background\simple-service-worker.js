// Simple Service Worker for testing
console.log('MarkDownload service worker starting...');

// Basic message listener
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Service worker received message:', message);
  
  if (message.type === 'test_download') {
    console.log('Test download request for:', message.tab?.title);
    sendResponse({success: true, message: 'Test successful'});
  }
  
  return true; // Keep message channel open for async response
});

// Basic installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('MarkDownload extension installed');
});

console.log('MarkDownload service worker loaded successfully');
