{"mcpServers": {"basic-memory": {"command": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\basic-memory.exe", "args": ["mcp"], "env": {"PYTHONIOENCODING": "utf-8", "PYTHONUNBUFFERED": "1", "BASIC_MEMORY_HOME": "C:\\Users\\<USER>\\basic-memory"}, "cwd": "C:\\Users\\<USER>", "autoApprove": true, "disabled": false}, "quickchart-server": {"command": "npx", "args": ["-y", "@gongrzhe/quickchart-mcp-server"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "tavily-mcp": {"command": "npx", "args": ["-y", "tavily-mcp"], "env": {"TAVILY_API_KEY": "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"}, "disabled": false, "autoApprove": []}, "filesystem": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "--root", "D:/360MoveData/Users/<USER>/Desktop"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "MiniMax": {"command": "uvx", "args": ["minimax-mcp"], "env": {"MINIMAX_GROUP_ID": "191676883242111081", "MINIMAX_API_KEY": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************.JbSIaS6KtmKQlEvy9gqrcNgDgUWq2ItHe3zJVLkrS1bBVDda6ugn9joMevOiPU9TnhlKJOfLeCe3nyYqaeWLhlrxXf__ubIT1ukYKE8zTndfrtm5BXy5Q_KVNC3xh9Sz609Zc24rUEWdUMTkXgRcmVg4qkSr_UxANJXoJo1R7GHodPezvvlIxcyyw7hG02dOmC1ZzoW9gn_kZSHDKMRj2p_FPctGZs9eYQSORns2-YDSzSC1BnqmJkNDAbj3xSQzQlBbzfNQLCDTTXYtMLOnMOLV2_zzlWfCDt2qI9dy3UHpl7LUF5jAMWjK0WkHQfUnc8MyNdDP22vouiGX9lIOCw", "MINIMAX_MCP_BASE_PATH": "D:/360MoveData/Users/<USER>/Desktop/minimax_output", "MINIMAX_API_HOST": "https://api.minimax.chat", "MINIMAX_API_RESOURCE_MODE": "local"}}, "amap-maps": {"command": "npx", "args": ["-y", "@amap/amap-maps-mcp-server"], "env": {"AMAP_MAPS_API_KEY": "c00d06c57e7ccc6f09bc829ba044969e"}}, "firecrawl-mcp": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-5605c0518e3c4116992b39c375898205"}}, "12306-mcp": {"command": "npx", "args": ["-y", "12306-mcp"]}, "edgeone-pages-mcp-server": {"command": "npx", "args": ["edgeone-pages-mcp"]}, "paperpal": {"command": "uv", "args": ["--directory", "C:\\Users\\<USER>\\.cursor-tutor\\paperpal-main\\paperpal", "run", "mcp_server.py"], "env": {"PYTHONIOENCODING": "utf-8", "PYTHONUNBUFFERED": "1", "MCP_CALL": "1"}, "autoApprove": true, "schema": {"search_papers": {"description": "Search for academic papers on arXiv and Semantic Scholar", "parameters": {"properties": {"query": {"type": "string", "description": "Search query for papers"}, "max_results": {"type": "integer", "description": "Maximum number of results to return", "default": 5}}, "required": ["query"]}}}}, "markitdown_mcp": {"command": "markitdown-mcp", "args": []}, "milvus": {"command": "node", "args": ["server.js"], "env": {"MILVUS_HOST": "**********", "MILVUS_PORT": "19530", "MILVUS_DB": "mcp_db", "MILVUS_COLLECTION": "full_text_search", "PORT": "3000"}, "cwd": "C:\\Users\\<USER>\\.cursor-tutor\\mcp-tool-server-milvus", "autoApprove": true}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "excel": {"command": "npx", "args": ["--yes", "@zhiweixu/excel-mcp-server"], "env": {"LOG_PATH": "C:\\\\Users\\\\<USER>\\\\.cursor-tutor\\\\logs"}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "cwd": "C:\\Users\\<USER>\\.cursor-tutor", "timeout": 600, "env": {"PYTHONIOENCODING": "utf-8", "PYTHONUNBUFFERED": "1", "MCP_DEBUG": "false", "MCP_WEB_PORT": "8765"}, "autoApprove": ["interactive_feedback"]}}}