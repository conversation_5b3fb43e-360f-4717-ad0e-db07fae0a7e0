// 内容脚本加载的第一行，立即报告已加载
console.log('元宝助手: 内容脚本正在加载...');

/**
 * 元宝助手 - 内容脚本
 * 在页面中检测元宝回答、提取内容并处理插入优化后的结果
 */

// 先插入CSS样式，防止样式重复声明错误
if (!document.getElementById('yuanbao-assistant-style')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'yuanbao-assistant-style';
  styleElement.textContent = `
  .yuanbao-optimized-answer {
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(76, 175, 80, 0.1);
  }
  
  .yuanbao-optimized-answer:hover {
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
  }
  `;
  document.head.appendChild(styleElement);
}

(function() {
  // 立即标记内容脚本已加载，便于调试
  window.__YUANBAO_ASSISTANT_LOADED__ = true;
  console.log('元宝助手: 内容脚本已加载');
  
  // 状态变量
  let processed = false;
  let extractionMode = 'full'; // 'thinking' 或 'full'
  let observerActive = false;
  let deepseekSelector = null;
  let detectionAttempts = 0; // 检测尝试次数
  let lastDetectionTime = 0; // 上次检测时间戳
  
  // 可能的元宝回答选择器列表 - 扩展更多选择器
  const potentialSelectors = [
    // DeepSeek/元宝可能的回答容器
    '.deepseek-response-text',
    '.deepseek-answer-container',
    '.deepseek-chat-answer',
    '.deepseek-result',
    '.answer-container',
    '.chat-message.answer',
    '[data-testid="deepseek-answer"]',
    // 更通用的选择器
    '.deepseek-chat .message:not(.user)',
    '.deepseek-output',
    '.AI-response',
    '.deepseek-response',
    '.message-list .message:not(.user-message)',
    '.chat-container .ai-message',
    // 尝试更广泛的选择器
    '.chat-container .message:not(.user-message)',
    '.message-content:not(.user-content)',
    // 内容区域通用选择器
    '.content-area div[class*="response"]',
    '.content-area div[class*="answer"]',
    // 更模糊的匹配
    'div[class*="deepseek"]',
    'div[class*="answer"]',
    'div[class*="response"]',
    // 各种可能的chat应用程序容器
    '.chat-view .answer',
    '.message-wrapper:not(.user)',
    '.conversation-container .ai-message',
    '.bot-response',
    '.response-content',
    '.message.ai',
    // 新增通用聊天UI选择器
    '.chat-area .message:not(.user-message)',
    '.message-container .message:not(.user-message)',
    '.thread-container .message:not(.user)',
    // 针对不同层级的嵌套内容
    '.chat-wrapper .message-wrapper .message-content:not(.user-content)',
    '.conversation [data-message-role="assistant"]',
    // 通过角色或属性选择
    '[data-role="assistant"]',
    '[data-sender="ai"]',
    '[data-message-type="bot"]',
    // 尝试非常通用的文本容器
    '.markdown-body',
    '.markdown-content',
    '.text-container'
  ];
  
  // 调试日志函数
  function debug(message, data = null) {
    const prefix = '元宝助手调试: ';
    if (data) {
      console.log(prefix + message, data);
    } else {
      console.log(prefix + message);
    }
  }
  
  // 立即执行初始化
  debug('内容脚本已加载');
  init();
  
  /**
   * 初始化函数
   */
  function init() {
    // 从存储中获取用户设置
    chrome.storage.sync.get({
      extractionMode: 'full'
    }, function(items) {
      extractionMode = items.extractionMode;
      debug('已加载设置，提取模式: ' + extractionMode);
      
      // 开始监控DOM变化
      startObserver();
      
      // 检查页面中是否已有元宝回答
      debug('开始检查页面中是否已有元宝回答');
      // 立即检查一次
      setTimeout(() => {
        const found = checkForExistingAnswer();
        debug('初始检查结果: ' + (found ? '找到回答' : '未找到回答'));
        
        // 如果没有找到，设置多次延迟检查
        if (!found) {
          scheduleDelayedChecks();
        }
      }, 1000);
    });
    
    // 监听来自弹出窗口的消息
    chrome.runtime.onMessage.addListener(handleMessages);
    debug('已设置消息监听器');

    // 添加心跳响应，确保内容脚本存活
    chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
      if (request.action === 'pingContentScript') {
        debug('收到ping请求，内容脚本活跃中');
        sendResponse({ alive: true, scriptVersion: '1.0', loadTime: new Date().toString() });
        return true;
      }
    });

    // 向背景脚本报告内容脚本已加载
    try {
      chrome.runtime.sendMessage({
        action: 'contentScriptLoaded',
        url: window.location.href,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('元宝助手: 报告内容脚本加载状态失败', error);
    }
  }
  
  /**
   * 处理消息
   */
  function handleMessages(request, sender, sendResponse) {
    debug('收到消息', request);
    switch(request.action) {
      case 'getDeepseekAnswer':
        const answer = extractAnswer(request.mode || extractionMode);
        debug('提取回答结果', answer ? '提取成功' : '提取失败');
        sendResponse({answer: answer});
        break;
        
      case 'setExtractionMode':
        extractionMode = request.mode;
        debug('设置提取模式为: ' + extractionMode);
        sendResponse({success: true});
        break;
        
      case 'insertResult':
        const success = insertOptimizedAnswer(request.result);
        debug('插入结果: ' + (success ? '成功' : '失败'));
        sendResponse({success: success});
        break;
        
      case 'checkForAnswer':
        // 手动检查时，使用更彻底的检测方法
        const hasAnswer = forceCheckForAnswer();
        debug('手动检查回答: ' + (hasAnswer ? '找到' : '未找到'));
        sendResponse({hasAnswer: hasAnswer});
        break;
        
      case 'checkYuanbaoAnswer':
        // 手动检查元宝回答并通知popup状态
        debug('执行检查元宝回答');
        
        // 立即回复确认已收到请求
        sendResponse({success: true, message: '正在检查元宝回答'});
        
        // 执行强制检查
        const found = forceCheckForAnswer();
        debug('检查结果: ' + (found ? '找到回答' : '未找到回答'));
        
        // 由于已经响应了sendResponse，现在需要使用runtime.sendMessage发送结果
        try {
          // 获取当前标签页ID（直接使用chrome.tabs而不是通过消息）
          chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            debug('获取当前标签信息', tabs && tabs.length > 0 ? tabs[0].id : 'tabs为空');
            
            const tabId = tabs && tabs.length > 0 ? tabs[0].id : null;
            
            if (found) {
              const answer = extractAnswer(extractionMode);
              if (answer) {
                debug('成功提取回答，长度: ' + answer.length);
                
                // 保存提取的回答和标签ID到本地存储
                chrome.storage.local.set({
                  yuanbaoAnswer: answer,
                  lastTabId: tabId
                }, function() {
                  debug('已保存回答到本地存储');
                });
                
                // 通知popup找到回答
                chrome.runtime.sendMessage({
                  action: 'yuanbaoAnswerFound',
                  answerLength: answer.length
                });
                debug('已通知popup找到元宝回答');
              } else {
                debug('虽然找到元素但未能提取内容');
                chrome.runtime.sendMessage({
                  action: 'yuanbaoAnswerNotFound',
                  reason: '找到元素但未能提取内容'
                });
              }
            } else {
              debug('未找到元宝回答');
              chrome.runtime.sendMessage({
                action: 'yuanbaoAnswerNotFound',
                reason: '未找到元宝回答元素'
              });
            }
          });
        } catch (error) {
          debug('通知popup时出错', error);
          // 即使出错也尝试通知popup
          try {
            chrome.runtime.sendMessage({
              action: 'yuanbaoAnswerNotFound',
              reason: '检查过程出错: ' + error.message
            });
          } catch (e) {
            debug('二次尝试通知popup也失败', e);
          }
        }
        
        // 已经使用sendResponse响应了
        return true;
    }
    return true; // 保持消息通道开放，用于异步响应
  }
  
  /**
   * 安排延迟检查
   * 在页面加载后的不同时间点尝试检测回答
   */
  function scheduleDelayedChecks() {
    // 设置多个延迟检查，以应对不同加载情况
    const delayTimes = [2000, 5000, 10000, 15000];
    
    delayTimes.forEach(delay => {
      setTimeout(() => {
        if (!processed) {
          debug(`执行延迟检查 (${delay}ms)`);
          checkForExistingAnswer();
        }
      }, delay);
    });
  }
  
  /**
   * 启动DOM变化观察者
   */
  function startObserver() {
    if (observerActive) return;
    
    const observer = new MutationObserver(function(mutations) {
      debug('检测到DOM变化，变化数量: ' + mutations.length);
      
      // 避免过于频繁的检查，设置节流
      const now = Date.now();
      if (now - lastDetectionTime < 1000) { // 至少间隔1秒
        return;
      }
      
      lastDetectionTime = now;
      checkForExistingAnswer();
    });
    
    observer.observe(document.body, { 
      childList: true, 
      subtree: true,
      characterData: true,
      attributeFilter: ['class', 'style'] // 监听可能影响可见性的属性变化
    });
    
    observerActive = true;
    debug('DOM观察者已启动');
  }
  
  /**
   * 强制检查页面中的所有可能元素
   * 更彻底的检测方法，用于手动检查
   */
  function forceCheckForAnswer() {
    debug('开始强制全面检查元素');
    
    // 重置深度选择器以便重新检测
    deepseekSelector = null;
    
    // 查找所有文本内容超过100个字符的元素
    const allElements = document.querySelectorAll('div, p, span, article, section');
    debug(`找到 ${allElements.length} 个可能的内容元素`);
    
    for (const element of allElements) {
      try {
        if (isElementVisible(element)) {
          const text = element.textContent.trim();
          if (text.length > 100 && !isUserMessage(element)) {
            debug('发现潜在回答元素:', {
              tagName: element.tagName,
              className: element.className,
              textLength: text.length,
              textPreview: text.substring(0, 50) + '...'
            });
            
            // 创建选择器
            let selector = createSelectorForElement(element);
            if (selector) {
              deepseekSelector = selector;
              notifyAnswerDetected();
              return true;
            }
          }
        }
      } catch (error) {
        continue; // 跳过处理错误的元素
      }
    }
    
    // 如果未找到，尝试标准的检测方法
    return checkForExistingAnswer();
  }
  
  /**
   * 为元素创建CSS选择器
   */
  function createSelectorForElement(element) {
    try {
      if (element.id) {
        return `#${element.id}`;
      }
      
      if (element.className && typeof element.className === 'string') {
        const classes = element.className.trim().split(/\s+/);
        if (classes.length > 0 && classes[0]) {
          return `.${classes[0]}`;
        }
      }
      
      // 尝试创建一个基于标签和父元素的选择器
      let selector = element.tagName.toLowerCase();
      let parent = element.parentElement;
      
      if (parent && parent.tagName) {
        if (parent.id) {
          return `#${parent.id} > ${selector}`;
        }
        
        if (parent.className && typeof parent.className === 'string') {
          const parentClasses = parent.className.trim().split(/\s+/);
          if (parentClasses.length > 0 && parentClasses[0]) {
            return `.${parentClasses[0]} > ${selector}`;
          }
        }
        
        return `${parent.tagName.toLowerCase()} > ${selector}`;
      }
      
      return selector;
    } catch (error) {
      debug('创建选择器时出错', error);
      return null;
    }
  }
  
  /**
   * 判断元素是否为用户消息
   */
  function isUserMessage(element) {
    // 检查元素及其父元素是否包含表示用户消息的类或属性
    let current = element;
    for (let i = 0; i < 3; i++) { // 检查当前元素及其两层父元素
      if (!current) break;
      
      // 检查类名
      const className = current.className || '';
      if (typeof className === 'string' && 
          (className.includes('user') || 
           className.includes('self') || 
           className.includes('me') || 
           className.includes('my-message'))) {
        return true;
      }
      
      // 检查角色属性
      if (current.getAttribute('data-role') === 'user' || 
          current.getAttribute('data-message-role') === 'user' ||
          current.getAttribute('data-sender') === 'user') {
        return true;
      }
      
      current = current.parentElement;
    }
    
    return false;
  }
  
  /**
   * 检查页面中是否存在元宝回答
   * @returns {boolean} 是否找到回答
   */
  function checkForExistingAnswer() {
    // 避免过多的检查尝试
    if (detectionAttempts > 20) {
      debug('已达到最大检测尝试次数');
      return false;
    }
    
    detectionAttempts++;
    
    // 如果已经处理过，不再重复检测
    if (processed && deepseekSelector !== null) {
      const answerElement = document.querySelector(deepseekSelector);
      if (answerElement && answerElement.textContent.trim().length > 0) {
        debug('使用已知选择器找到回答: ' + deepseekSelector);
        return true;
      }
    }
    
    // 尝试各种可能的选择器
    debug('开始尝试所有潜在选择器');
    for (const selector of potentialSelectors) {
      try {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          debug('选择器 ' + selector + ' 匹配到 ' + elements.length + ' 个元素');
        }
        
        for (const element of elements) {
          // 检查元素是否可见且有内容
          if (isElementVisible(element)) {
            const text = element.textContent.trim();
            const textLength = text.length;
            
            if (textLength > 50) {
              debug('找到可能的回答元素', {
                selector: selector,
                textLength: textLength,
                textPreview: text.substring(0, 50) + (textLength > 50 ? '...' : '')
              });
              
              // 确认不是用户消息
              if (!isUserMessage(element)) {
                deepseekSelector = selector;
                debug('找到有效回答！选择器: ' + selector);
                notifyAnswerDetected();
                return true;
              } else {
                debug('跳过用户消息元素');
              }
            }
          }
        }
      } catch (error) {
        debug('处理选择器时出错: ' + selector, error);
      }
    }
    
    // 检查iframe
    try {
      const frames = document.querySelectorAll('iframe');
      debug('检查 ' + frames.length + ' 个iframe');
      
      for (const frame of frames) {
        try {
          // 尝试访问iframe内容 (受同源策略限制)
          const frameDoc = frame.contentDocument || frame.contentWindow.document;
          for (const selector of potentialSelectors) {
            const elements = frameDoc.querySelectorAll(selector);
            
            for (const element of elements) {
              if (isElementVisible(element)) {
                const text = element.textContent.trim();
                if (text.length > 50 && !isUserMessage(element)) {
                  debug('在iframe中找到回答!', {
                    selector: selector,
                    textLength: text.length
                  });
                  
                  // 需要特殊处理iframe中的元素
                  deepseekSelector = {
                    frameIndex: Array.from(frames).indexOf(frame),
                    selector: selector
                  };
                  
                  notifyAnswerDetected();
                  return true;
                }
              }
            }
          }
        } catch (frameError) {
          // 跨域iframe会抛出错误，这是预期的
          continue;
        }
      }
    } catch (iframeError) {
      debug('检查iframe时出错', iframeError);
    }
    
    debug('未找到元宝回答');
    return false;
  }
  
  /**
   * 检查元素是否可见
   */
  function isElementVisible(element) {
    if (!element) return false;
    
    try {
      const style = window.getComputedStyle(element);
      
      // 检查基本可见性
      if (style.display === 'none' || 
          style.visibility === 'hidden' || 
          style.opacity === '0') {
        return false;
      }
      
      // 检查元素尺寸
      const rect = element.getBoundingClientRect();
      if (rect.width <= 1 || rect.height <= 1) {
        return false;
      }
      
      // 检查元素是否在可滚动区域内（即使不在视口中）
      // 放宽检查范围，只要在页面的合理范围内就算可见
      if (rect.bottom < -2000 || 
          rect.top > (window.innerHeight + 2000) || 
          rect.right < -500 || 
          rect.left > (window.innerWidth + 500)) {
        return false;
      }
      
      // 检查是否被其他元素完全覆盖
      // 这个检查可能较重，暂时简化处理
      
      return true;
    } catch (error) {
      debug('检查元素可见性时出错', error);
      return false;
    }
  }
  
  /**
   * 通知背景脚本检测到回答
   */
  function notifyAnswerDetected() {
    if (processed) {
      debug('已处理过，不再发送通知');
      return;
    }
    
    debug('发送回答检测通知到背景脚本');
    chrome.runtime.sendMessage({
      action: 'answerDetected'
    }, function(response) {
      debug('背景脚本响应', response);
    });
    
    processed = true;
    debug('回答检测状态已更新为已处理');
  }
  
  /**
   * 提取元宝回答文本
   * @param {string} mode - 提取模式 ('thinking' 或 'full')
   * @returns {string|null} 提取的文本或null
   */
  function extractAnswer(mode) {
    debug('开始提取回答，模式: ' + mode);
    if (!deepseekSelector) {
      debug('尚未确定选择器，尝试查找元素');
      
      // 尝试强制检查
      forceCheckForAnswer();
      
      if (!deepseekSelector) {
        debug('强制检查后仍未找到元素');
        return null;
      }
    }
    
    let answerElement = null;
    let fullText = '';
    
    // 处理iframe中的元素
    if (typeof deepseekSelector === 'object' && deepseekSelector.frameIndex !== undefined) {
      try {
        const frames = document.querySelectorAll('iframe');
        const frame = frames[deepseekSelector.frameIndex];
        if (frame) {
          const frameDoc = frame.contentDocument || frame.contentWindow.document;
          answerElement = frameDoc.querySelector(deepseekSelector.selector);
        }
      } catch (error) {
        debug('从iframe中提取元素时出错', error);
      }
    } else {
      // 处理主文档中的元素
      answerElement = document.querySelector(deepseekSelector);
    }
    
    if (!answerElement) {
      debug('未找到回答元素');
      return null;
    }
    
    fullText = answerElement.textContent.trim();
    debug('提取到原始文本，长度: ' + fullText.length);
    
    if (mode === 'thinking') {
      // 尝试提取思考部分
      const thinkingPart = extractThinkingPart(fullText);
      debug('已提取思考部分，长度: ' + thinkingPart.length);
      return thinkingPart;
    } else {
      // 提取完整回答
      debug('使用完整回答');
      return fullText;
    }
  }
  
  /**
   * 使用启发式方法提取思考部分
   * @param {string} text - 完整文本
   * @returns {string} 提取的思考部分
   */
  function extractThinkingPart(text) {
    // 寻找常见的思考标记
    const thinkingPatterns = [
      // 中文思考标记
      /让我思考一下[\s\S]*?(?=结论|总结|回答|因此)/i,
      /我需要分析[\s\S]*?(?=基于以上分析|因此|所以|总结|回答)/i,
      /思考过程[\s\S]*?(?=最终答案|因此|总结|回答)/i,
      /首先[\s\S]*?(?=综上所述|总结来看|因此|回答)/i,
      
      // 英文思考标记
      /Let me think[\s\S]*?(?=Therefore|In conclusion|To summarize)/i,
      /I need to analyze[\s\S]*?(?=Based on the above|Therefore|So|In conclusion)/i,
      /My reasoning[\s\S]*?(?=The answer|Therefore|In conclusion)/i,
      /First[\s\S]*?(?=In summary|To conclude|Therefore)/i
    ];
    
    // 尝试使用预定义模式提取
    for (const pattern of thinkingPatterns) {
      const match = text.match(pattern);
      if (match && match[0].length > 50) {
        debug('通过模式提取到思考部分: ' + pattern);
        return match[0];
      }
    }
    
    // 如果没有匹配标记，使用启发式方法
    // 1. 查找<think>标签
    const thinkTagMatch = text.match(/<think>([\s\S]*?)<\/think>/);
    if (thinkTagMatch && thinkTagMatch[1].length > 50) {
      debug('通过<think>标签提取到思考部分');
      return thinkTagMatch[1];
    }
    
    // 2. 尝试提取前40%的内容作为思考部分
    const thinkingPortion = Math.floor(text.length * 0.4);
    const extractedThinking = text.substring(0, thinkingPortion);
    
    debug('通过比例提取思考部分(前40%)');
    return extractedThinking;
  }
  
  /**
   * 将优化后的回答插入页面
   * @param {string} optimizedText - 优化后的文本
   * @returns {boolean} 是否成功插入
   */
  function insertOptimizedAnswer(optimizedText) {
    debug('尝试插入优化回答');
    if (!optimizedText) {
      debug('无法插入：缺少优化文本');
      return false;
    }
    
    if (!deepseekSelector) {
      debug('无法插入：未找到选择器，尝试重新检测');
      if (!forceCheckForAnswer()) {
        debug('即使强制检查也未找到元素');
        // 尝试在最合理的位置插入
        return insertOptimizedAnswerFallback(optimizedText);
      }
    }
    
    let answerContainer = null;
    
    // 处理iframe中的元素
    if (typeof deepseekSelector === 'object' && deepseekSelector.frameIndex !== undefined) {
      try {
        const frames = document.querySelectorAll('iframe');
        const frame = frames[deepseekSelector.frameIndex];
        if (frame) {
          const frameDoc = frame.contentDocument || frame.contentWindow.document;
          answerContainer = frameDoc.querySelector(deepseekSelector.selector);
        }
      } catch (error) {
        debug('获取iframe中元素时出错', error);
        return insertOptimizedAnswerFallback(optimizedText);
      }
    } else {
      // 处理主文档中的元素
      answerContainer = document.querySelector(deepseekSelector);
    }
    
    if (!answerContainer) {
      debug('无法找到回答容器元素');
      return insertOptimizedAnswerFallback(optimizedText);
    }
    
    // 检查是否已经插入过优化回答
    const existingOptimized = document.querySelector('.yuanbao-optimized-answer');
    if (existingOptimized) {
      debug('移除已存在的优化回答');
      existingOptimized.remove();
    }
    
    // 创建优化回答的容器
    const optimizedElement = document.createElement('div');
    optimizedElement.className = 'yuanbao-optimized-answer';
    optimizedElement.style.cssText = 'border: 2px solid #4CAF50; border-radius: 8px; padding: 16px; margin-top: 20px; background-color: #f9fff9; position: relative;';
    
    // 添加标题
    const titleElement = document.createElement('div');
    titleElement.style.cssText = 'color: #4CAF50; font-weight: bold; margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;';
    
    const titleText = document.createElement('span');
    titleText.textContent = '🌟 元宝助手优化回答:';
    titleElement.appendChild(titleText);
    
    // 添加关闭按钮
    const closeButton = document.createElement('button');
    closeButton.textContent = '✕';
    closeButton.style.cssText = 'background: none; border: none; color: #666; cursor: pointer; font-size: 16px;';
    closeButton.addEventListener('click', function() {
      optimizedElement.remove();
    });
    titleElement.appendChild(closeButton);
    
    // 添加内容
    const contentElement = document.createElement('div');
    contentElement.style.cssText = 'line-height: 1.6; white-space: pre-wrap;';
    contentElement.textContent = optimizedText;
    
    // 添加底部署名
    const footerElement = document.createElement('div');
    footerElement.style.cssText = 'margin-top: 15px; font-size: 12px; color: #666; text-align: right;';
    footerElement.textContent = '由元宝助手优化 - 通过Gemini模型';
    
    // 组装元素
    optimizedElement.appendChild(titleElement);
    optimizedElement.appendChild(contentElement);
    optimizedElement.appendChild(footerElement);
    
    // 插入到原回答之后
    debug('开始将优化回答插入DOM');
    try {
      answerContainer.parentNode.insertBefore(optimizedElement, answerContainer.nextSibling);
      debug('成功插入优化回答');
      return true;
    } catch (error) {
      debug('插入优化回答时出错', error);
      // 尝试其他插入方法
      try {
        answerContainer.insertAdjacentElement('afterend', optimizedElement);
        debug('使用备用方法成功插入优化回答');
        return true;
      } catch (fallbackError) {
        debug('备用插入方法也失败', fallbackError);
        return insertOptimizedAnswerFallback(optimizedText);
      }
    }
  }
  
  /**
   * 备用方法：在合理位置插入优化回答
   */
  function insertOptimizedAnswerFallback(optimizedText) {
    debug('使用备用方法插入回答');
    
    // 检查是否已经插入过优化回答
    const existingOptimized = document.querySelector('.yuanbao-optimized-answer');
    if (existingOptimized) {
      existingOptimized.remove();
    }
    
    // 创建优化回答的容器
    const optimizedElement = document.createElement('div');
    optimizedElement.className = 'yuanbao-optimized-answer';
    optimizedElement.style.cssText = 'border: 2px solid #4CAF50; border-radius: 8px; padding: 16px; margin: 20px auto; background-color: #f9fff9; position: relative; max-width: 800px; z-index: 9999;';
    
    // 添加标题
    const titleElement = document.createElement('div');
    titleElement.style.cssText = 'color: #4CAF50; font-weight: bold; margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;';
    
    const titleText = document.createElement('span');
    titleText.textContent = '🌟 元宝助手优化回答:';
    titleElement.appendChild(titleText);
    
    // 添加关闭按钮
    const closeButton = document.createElement('button');
    closeButton.textContent = '✕';
    closeButton.style.cssText = 'background: none; border: none; color: #666; cursor: pointer; font-size: 16px;';
    closeButton.addEventListener('click', function() {
      optimizedElement.remove();
    });
    titleElement.appendChild(closeButton);
    
    // 添加内容
    const contentElement = document.createElement('div');
    contentElement.style.cssText = 'line-height: 1.6; white-space: pre-wrap;';
    contentElement.textContent = optimizedText;
    
    // 添加底部署名
    const footerElement = document.createElement('div');
    footerElement.style.cssText = 'margin-top: 15px; font-size: 12px; color: #666; text-align: right;';
    footerElement.textContent = '由元宝助手优化 - 通过Gemini模型';
    
    // 组装元素
    optimizedElement.appendChild(titleElement);
    optimizedElement.appendChild(contentElement);
    optimizedElement.appendChild(footerElement);
    
    // 尝试以下插入位置:
    // 1. 主要内容区域
    const mainContentCandidates = [
      document.querySelector('main'),
      document.querySelector('.main-content'),
      document.querySelector('.content-area'),
      document.querySelector('.chat-content'),
      document.querySelector('#content'),
      document.querySelector('.container')
    ];
    
    for (const container of mainContentCandidates) {
      if (container) {
        try {
          container.appendChild(optimizedElement);
          debug('成功插入到内容区域');
          return true;
        } catch (error) {
          continue;
        }
      }
    }
    
    // 2. 最后尝试直接插入到body
    try {
      document.body.appendChild(optimizedElement);
      // 确保滚动到可见区域
      optimizedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      debug('成功插入到body');
      return true;
    } catch (error) {
      debug('所有插入方法都失败', error);
      return false;
    }
  }
  
  // 页面加载后执行多次检查
  window.addEventListener('load', function() {
    debug('页面完全加载，执行额外检查');
    
    // 立即执行一次检查
    setTimeout(function() {
      checkForExistingAnswer();
    }, 500);
    
    // 设置递增间隔的多次检查
    const intervals = [1000, 2000, 3000, 5000, 8000, 13000];
    intervals.forEach(interval => {
      setTimeout(function() {
        if (!processed) {
          debug(`执行页面加载后的延迟检查 (${interval}ms)`);
          checkForExistingAnswer();
        }
      }, interval);
    });
  });
  
})();

// 全局公开API，便于直接在页面调试
window.__yuanbaoAssistant__ = {
  version: '1.0.0',
  check: function() {
    console.log('元宝助手: 手动触发检查');
    const event = new CustomEvent('yuanbao-assistant-check');
    document.dispatchEvent(event);
    return '检查已触发';
  },
  status: function() {
    return {
      loaded: true,
      selector: window.__YUANBAO_ASSISTANT_SELECTOR__ || null,
      processed: window.__YUANBAO_ASSISTANT_PROCESSED__ || false
    };
  }
};

// 添加调试信息
console.log('元宝助手: 内容脚本完全加载完成'); 