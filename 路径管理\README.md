# 🚀 路径管理器

一个现代化设计的路径快速访问工具，采用浅蓝色主题，支持本地文件夹和网址的快速打开。

## ✨ 全新UI设计

- 🎨 **现代浅蓝色主题** - 清新优雅的视觉体验
- 🖱️ **悬停效果** - 按钮交互反馈更直观
- 📱 **响应式布局** - 窗口大小可调整
- 🎯 **图标化界面** - 直观的emoji图标标识
- 🌈 **交替行颜色** - 列表阅读更清晰
- ✨ **现代化对话框** - 美观的编辑界面

## 🚀 使用方法

1. **直接启动**：双击 `启动路径管理器.bat` 文件
2. **手动启动**：运行 `python path_manager.py`
3. **环境检测**：运行 `python 测试环境.py` 检查环境

## 🎯 功能特点

- ✅ 快速访问常用文件夹和网址
- ✅ 现代化可视化管理界面
- ✅ 支持添加、编辑、删除路径
- ✅ 配置自动保存到JSON文件
- ✅ 双击快速打开路径
- ✅ 支持文件夹浏览选择
- ✅ 图标化类型显示
- ✅ 悬停按钮效果

## 📋 操作说明

### 🎮 基本操作
- **🚀 打开路径**：双击列表中的项目或选中后点击"🚀 打开"按钮
- **➕ 添加路径**：点击"➕ 添加"按钮，在弹出对话框中输入信息
- **✏️ 编辑路径**：选中项目后点击"✏️ 编辑"按钮修改信息
- **🗑️ 删除路径**：选中项目后点击"🗑️ 删除"按钮移除路径

### 🏷️ 路径类型
- **📁 文件夹**：本地文件夹路径，点击后用文件管理器打开
- **🌐 网址**：网页链接，点击后用默认浏览器打开

### 🎨 界面特色
- **浅蓝色主题**：清新的#E3F2FD背景色
- **悬停效果**：鼠标悬停时按钮颜色变化
- **图标标识**：每种类型都有对应的emoji图标
- **交替行色**：列表项交替显示不同背景色

## 默认预设

程序首次运行时会自动创建以下预设路径：
- 桌面文件夹
- 文档文件夹  
- 下载文件夹
- 百度网盘（示例网址）

## 配置文件

程序会在同目录下创建 `path_config.json` 文件保存您的路径配置，无需手动编辑。

## 系统要求

- Windows 系统
- Python 3.6 或更高版本
- tkinter 库（Python 标准库）

## 注意事项

- 确保 Python 已正确安装并添加到系统 PATH
- 网址路径请包含完整的 http:// 或 https:// 前缀
- 本地路径请确保文件夹存在，否则会提示错误
