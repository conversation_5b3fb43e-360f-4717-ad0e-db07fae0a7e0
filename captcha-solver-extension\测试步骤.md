# 🤖 AI验证码破解助手 - 测试指南

## 📋 安装步骤

### 1. 在Chrome中加载插件
1. 打开Chrome浏览器
2. 地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `captcha-solver-extension` 文件夹
6. 确认插件已成功加载（应该看到插件图标）

### 2. 配置API密钥
1. 点击浏览器工具栏中的插件图标 🤖
2. 在弹出的窗口中输入您的Gemini API密钥
3. 点击"💾 保存设置"按钮
4. 确认显示"设置保存成功！"

## 🧪 功能测试

### 测试1：基础界面测试
1. 点击插件图标，检查弹窗是否正常显示
2. 测试各个按钮是否响应
3. 检查设置保存/加载功能

### 测试2：验证码检测测试
1. 访问包含reCAPTCHA的网站，例如：
   - Google账户登录页面
   - 一些注册表单页面
   - reCAPTCHA演示页面
2. 点击插件中的"🔍 检测验证码"按钮
3. 观察是否能正确检测到验证码

### 测试3：完整破解流程测试
1. 在有reCAPTCHA的页面上
2. 点击"🚀 破解验证码"按钮
3. **重点观察**：页面右上角会出现详细的进度显示器

## 📊 进度显示功能

插件会在页面右上角显示一个漂亮的进度窗口，包含：

### 8个详细步骤：
- 🔍 **步骤1**: 检测验证码
- 🖱️ **步骤2**: 点击"我不是机器人"复选框
- ⏳ **步骤3**: 等待图像验证码加载
- 📸 **步骤4**: 截取验证码图像
- 🤖 **步骤5**: AI正在分析图像
- 🎯 **步骤6**: 点击识别出的图像
- 📤 **步骤7**: 提交验证结果
- 🔍 **步骤8**: 检查验证结果

### 进度显示特性：
- ✅ 实时进度条和百分比
- 📝 详细的状态信息
- 🎨 美观的渐变UI设计
- ❌ 错误信息和警告提示
- 🔄 可关闭的进度窗口

## 🎯 测试重点

### 1. 进度显示测试
- 确认每个步骤都有清晰的图标和描述
- 检查进度条是否正确更新
- 验证详细状态信息是否显示

### 2. 错误处理测试
- 测试无API密钥时的错误提示
- 测试无验证码页面时的处理
- 测试网络错误时的反馈

### 3. UI响应测试
- 检查进度窗口的动画效果
- 测试关闭按钮功能
- 验证状态颜色变化（成功/错误/警告）

## ⚠️ 可能遇到的问题

### 1. API相关
- 需要有效的Gemini API密钥
- 确保API配额充足
- 检查网络连接

### 2. 权限问题
- 某些网站可能阻止插件运行
- 跨域iframe访问限制

### 3. 兼容性
- 不同版本的reCAPTCHA可能有差异
- 某些网站的特殊实现

## 📝 测试记录

建议记录以下信息：
- [ ] 插件安装是否成功
- [ ] 弹窗界面是否正常
- [ ] 验证码检测是否准确
- [ ] 进度显示是否完整
- [ ] 破解成功率如何
- [ ] 遇到的错误和问题

## 🔧 调试技巧

1. **查看控制台**：按F12打开开发者工具，查看Console标签
2. **检查网络请求**：在Network标签中查看API调用
3. **插件调试**：在扩展程序页面点击"检查视图"

## 🎉 成功标志

如果看到以下情况，说明插件工作正常：
- ✅ 进度窗口正确显示8个步骤
- ✅ 每个步骤都有详细的状态更新
- ✅ 验证码能够被正确识别和点击
- ✅ 最终显示"🎉 验证码破解成功！"
