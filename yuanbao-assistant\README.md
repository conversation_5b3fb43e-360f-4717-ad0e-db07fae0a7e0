# 元宝助手 (<PERSON><PERSON> Assistant)

元宝助手是一个强大的Chrome浏览器扩展，它能够拦截元宝（DeepSeek）的回答，利用多个AI模型（如Gemini、Claude、GPT等）对回答进行优化，让您获得更清晰、更有条理的回答。

![元宝助手示例](screenshots/preview.png)

## 功能特点

- **双模式提取**：支持提取完整回答或仅思考部分进行优化
- **多模型支持**：内置支持Gemini、Claude和GPT模型
- **自定义提示词**：允许用户自定义优化提示词
- **实时监测**：自动检测网页中的元宝回答
- **美观集成**：优化后的回答无缝集成到原网页中
- **结果管理**：支持复制、保存优化结果

## 安装方法

### 1. 从Chrome商店安装（即将上线）

- 访问[Chrome网上应用店](https://chrome.google.com/webstore)
- 搜索"元宝助手"并安装

### 2. 开发模式安装

1. 下载本仓库的zip文件，或使用git克隆：
   ```
   git clone https://github.com/yourusername/yuanbao-assistant.git
   ```

2. 在Chrome浏览器中打开扩展程序页面：
   - 地址栏输入：`chrome://extensions/`
   - 或者通过菜单：设置 > 扩展程序

3. 开启开发者模式（右上角的开关）

4. 点击"加载已解压的扩展程序"，选择项目文件夹

## 初始设置

首次使用前，您需要配置至少一个API密钥：

1. 安装扩展后，点击工具栏上的元宝助手图标
2. 点击"⚙️ 设置"打开设置页面
3. 输入API密钥：
   - Gemini API密钥（必需）：[获取地址](https://aistudio.google.com/app/apikey)
   - Claude API密钥（可选）：[获取地址](https://console.anthropic.com/keys)
   - GPT API密钥（可选）：[获取地址](https://platform.openai.com/api-keys)
4. 保存设置

## 使用方法

1. 访问含有元宝（DeepSeek）回答的网页
2. 当元宝生成回答后，扩展图标将变为黄色，表示检测到回答
3. 点击扩展图标，打开弹出窗口
4. 选择提取模式：
   - 完整回答：提取整个回答文本
   - 思考部分：仅提取推理思考部分
5. 选择要使用的AI模型
6. 根据需要编辑提示词
7. 点击"提交给AI优化"按钮
8. 得到优化结果后，您可以：
   - 复制结果
   - 保存到收藏
   - 复制

## 配置说明

在设置页面，您可以配置以下选项：

- **API密钥**：设置各个AI模型的API密钥
- **默认提取模式**：选择默认从元宝回答中提取的内容
- **默认使用模型**：选择默认使用的AI模型
- **默认提示词**：设置默认的提示词模板
- **自动检测**：开启或关闭自动检测元宝回答
- **保存历史**：开启或关闭保存优化历史记录

## 常见问题

**Q: 为什么无法检测到元宝回答？**  
A: 元宝网站的DOM结构可能会发生变化。请尝试刷新页面，或者将此问题报告给我们。

**Q: API调用失败是什么原因？**  
A: 请检查您的API密钥是否正确，以及对应服务是否可用。需要注意的是，某些API可能有区域限制或使用配额。

**Q: 如何提供反馈？**  
A: 请在GitHub仓库的Issues页面提交反馈或问题。

## 隐私声明

元宝助手将您的API密钥存储在Chrome的安全存储中，不会发送到任何外部服务器。对元宝回答的处理完全在您的浏览器中完成，仅将必要的内容发送到您配置的AI API服务。

## 开源协议

本项目采用 MIT 协议开源，详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎贡献代码或提出改进建议！请参考 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详情。

## 致谢

感谢所有参与本项目开发和测试的贡献者，以及提供底层服务的Google、Anthropic和OpenAI等公司。 