<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 16px;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .section h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
        }
        
        input {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        
        button {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            margin-bottom: 8px;
            font-weight: bold;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-secondary {
            background: #2196F3;
            color: white;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 12px;
            text-align: center;
            display: none;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.3);
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.3);
        }

        .execution-status {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
            display: none;
        }

        .execution-status.active {
            display: block;
        }

        .step-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: #4CAF50;
            border-radius: 3px;
            width: 0%;
            transition: width 0.3s ease;
        }

        .step-details {
            font-size: 11px;
            margin-bottom: 10px;
        }

        .step-list {
            font-size: 10px;
        }

        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
        }

        .step-icon {
            margin-right: 6px;
            width: 12px;
        }

        .step-item.completed .step-icon {
            color: #4CAF50;
        }

        .step-item.current .step-icon {
            color: #2196F3;
        }

        .step-item.pending .step-icon {
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI验证码破解助手</h1>
    </div>
    
    <div class="section">
        <h3>⚙️ 设置</h3>
        <input type="password" id="apiKey" placeholder="输入Gemini API密钥">
        <button class="btn-secondary" id="saveBtn">💾 保存设置</button>
    </div>
    
    <div class="section">
        <h3>🎯 操作</h3>
        <button class="btn-secondary" id="detectBtn">🔍 检测验证码</button>
        <button class="btn-primary" id="solveBtn">🚀 破解验证码</button>
        <button class="btn-secondary" id="manualBtn">🖱️ 手动模式</button>
        
        <!-- 执行状态显示 -->
        <div id="executionStatus" class="execution-status">
            <div class="step-info">
                <div id="currentStepText">准备开始...</div>
                <div id="stepPercentage">0%</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="step-details" id="stepDetails">等待开始执行...</div>
            
            <div class="step-list">
                <div class="step-item pending" id="step1">
                    <div class="step-icon">⏳</div>
                    <div>检测验证码</div>
                </div>
                <div class="step-item pending" id="step2">
                    <div class="step-icon">⏳</div>
                    <div>点击复选框</div>
                </div>
                <div class="step-item pending" id="step3">
                    <div class="step-icon">⏳</div>
                    <div>等待加载</div>
                </div>
                <div class="step-item pending" id="step4">
                    <div class="step-icon">⏳</div>
                    <div>截取图像</div>
                </div>
                <div class="step-item pending" id="step5">
                    <div class="step-icon">⏳</div>
                    <div>AI分析</div>
                </div>
                <div class="step-item pending" id="step6">
                    <div class="step-icon">⏳</div>
                    <div>点击图像</div>
                </div>
                <div class="step-item pending" id="step7">
                    <div class="step-icon">⏳</div>
                    <div>提交验证</div>
                </div>
                <div class="step-item pending" id="step8">
                    <div class="step-icon">⏳</div>
                    <div>检查结果</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="status" id="status"></div>
    
    <script src="popup-simple.js"></script>
</body>
</html>
