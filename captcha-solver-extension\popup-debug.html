<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .config-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        
        .config-section h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #e0e0e0;
        }
        
        .input-group {
            margin-bottom: 10px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #f0f0f0;
        }
        
        .input-group input {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 12px;
            box-sizing: border-box;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 12px;
            display: none;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            color: #c8e6c9;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.3);
            color: #ffcdd2;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.3);
            color: #bbdefb;
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.3);
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: #4CAF50;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .debug-info {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 10px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI验证码破解助手 (调试版)</h1>
    </div>
    
    <div class="config-section">
        <h3>⚙️ API配置</h3>
        <div class="input-group">
            <label for="apiKey">Gemini API密钥:</label>
            <input type="password" id="apiKey" placeholder="输入你的Gemini API密钥">
        </div>
        <div class="input-group">
            <label for="autoMode">自动模式:</label>
            <label class="toggle-switch">
                <input type="checkbox" id="autoMode">
                <span class="slider"></span>
            </label>
        </div>
    </div>
    
    <div class="config-section">
        <h3>🎯 操作控制</h3>
        <div class="button-group">
            <button class="btn btn-primary" id="solveBtn">🚀 破解验证码</button>
            <button class="btn btn-secondary" id="detectBtn">🔍 检测验证码</button>
        </div>
        <div class="button-group">
            <button class="btn btn-secondary" id="saveBtn">💾 保存设置</button>
            <button class="btn btn-secondary" id="resetBtn">🔄 重置</button>
        </div>
    </div>
    
    <div class="status" id="status"></div>
    
    <div class="debug-info" id="debugInfo">
        调试信息将显示在这里...
    </div>
    
    <script src="popup-debug.js"></script>
</body>
</html>
