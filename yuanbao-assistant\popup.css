/* 基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --primary-color: #4CAF50;
  --primary-light: #81C784;
  --primary-dark: #388E3C;
  --primary-bg: #E8F5E9;
  --accent-color: #1B5E20;
  --text-dark: #212121;
  --text-light: #757575;
  --white: #FFFFFF;
  --border-color: #DDDDDD;
}

body {
  width: 380px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  padding: 0;
  overflow-x: hidden;
  background-color: var(--primary-bg);
  color: var(--text-dark);
}

.container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--primary-light);
}

/* 头部样式 */
.header {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 2px solid var(--primary-light);
  margin-bottom: 15px;
  position: relative;
}

.header-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
  align-items: center;
}

.logo {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

/* 文字logo样式 */
.text-logo {
  width: 36px;
  height: 36px;
  margin-right: 12px;
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: bold;
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

h3 {
  font-size: 22px;
  font-weight: 600;
  color: var(--primary-dark);
}

.version {
  font-size: 14px;
  margin-left: 4px;
  color: var(--primary-color);
  font-weight: normal;
  vertical-align: top;
}

/* 状态指示器 */
.status {
  padding: 10px 14px;
  border-radius: 6px;
  text-align: center;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.waiting {
  background-color: #f0f0f0;
  color: var(--text-light);
}

.detected {
  background-color: #fff3cd;
  color: #856404;
  border-left: 4px solid #ffeeba;
}

.processing {
  background-color: #cce5ff;
  color: #004085;
  border-left: 4px solid #b8daff;
}

.completed {
  background-color: var(--primary-bg);
  color: var(--primary-dark);
  border-left: 4px solid var(--primary-color);
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  border-left: 4px solid #f5c6cb;
}

/* 手动检测按钮 */
.check-btn {
  margin-bottom: 12px;
  background-color: var(--primary-color);
  color: white;
  padding: 8px 16px;
  width: 100%;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.check-btn:hover {
  background-color: var(--primary-dark);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.check-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* 模式选择器 */
.mode-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
  background-color: var(--primary-bg);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--primary-light);
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mode-label {
  font-size: 13px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* 模型选择器 */
.model-selector {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.model-selector select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--primary-light);
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  transition: all 0.2s;
}

.model-selector select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.15rem rgba(76, 175, 80, 0.25);
}

/* 提示词部分 */
.prompt-section {
  margin-bottom: 12px;
}

.prompt-section label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-dark);
}

textarea {
  width: 100%;
  height: 80px;
  padding: 10px;
  border: 1px solid var(--primary-light);
  border-radius: 6px;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
  font-family: inherit;
  transition: all 0.3s;
  background-color: var(--white);
}

textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.15rem rgba(76, 175, 80, 0.25);
}

/* 按钮样式 */
button {
  cursor: pointer;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
  padding: 12px 18px;
  width: 100%;
  font-size: 15px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.primary-btn:hover {
  background-color: var(--primary-dark);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.primary-btn:disabled {
  background-color: #cccccc;
  box-shadow: none;
  transform: none;
  cursor: not-allowed;
}

.secondary-btn {
  background-color: #6c757d;
  color: white;
  padding: 8px 14px;
  width: 100%;
  margin-top: 8px;
}

.secondary-btn:hover {
  background-color: #5a6268;
}

.icon-btn {
  background-color: transparent;
  padding: 4px 8px;
  font-size: 16px;
}

.icon-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 新增的更显眼的操作按钮 */
.action-btn {
  background-color: #f0f0f0;
  color: #333;
  padding: 6px 10px;
  font-size: 14px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.action-btn:hover {
  background-color: #e0e0e0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 复制按钮的悬停效果 */
.action-btn:hover .copy-icon::before,
.action-btn:hover .copy-icon::after {
  border-color: #007bff;
}

/* 复制成功状态 */
.action-btn.copy-success {
  background-color: #e8f5e9;
  color: #4CAF50;
  transition: background-color 0.3s ease;
}

.action-btn.copy-success .copy-icon::before,
.action-btn.copy-success .copy-icon::after {
  border-color: #4CAF50;
}

/* 添加一个简单的成功动画效果 */
@keyframes copySuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.action-btn.copy-success {
  animation: copySuccess 0.3s ease;
}

/* 结果显示区 */
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 8px;
}

/* API状态显示 */
.api-status-container {
  margin-top: 15px;
  margin-bottom: 15px;
  border: 1px solid var(--primary-light);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.api-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  background-color: var(--primary-bg);
  border-bottom: 1px solid var(--primary-light);
}

.api-status-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.api-status {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 12px;
  margin-top: 6px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.api-status.waiting .status-indicator {
  background-color: #ffd700;
}

.api-status.loading .status-indicator {
  background-color: #2196F3;
}

.api-status.success .status-indicator {
  background-color: #4CAF50;
}

.api-status.error .status-indicator {
  background-color: #f44336;
}

.status-message {
  flex: 1;
  min-width: 0;
  font-size: 14px;
}

.api-error-details {
  padding: 10px 12px;
  background-color: #ffebee;
  border-top: 1px solid #ffcdd2;
  font-size: 13px;
  color: #b71c1c;
  white-space: pre-wrap;
  max-height: 100px;
  overflow-y: auto;
}

.small-btn {
  background-color: #e0e0e0;
  color: #333;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.small-btn:hover {
  background-color: #d0d0d0;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;
}

.loading-message {
  white-space: nowrap;
  font-size: 14px;
  color: #333;
}

.loading-dots {
  display: inline-block;
  min-width: 24px;
  position: relative;
  height: 16px;
  overflow: hidden;
}

.loading-dots::after {
  content: '...';
  position: absolute;
  left: 0;
  animation: loadingDots 1.2s steps(4, jump-none) infinite;
  letter-spacing: 4px;
}

.loading-substatus {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  min-height: 16px;
}

@keyframes loadingDots {
  0%, 20% {
    transform: translateX(-24px);
  }
  80%, 100% {
    transform: translateX(0);
  }
}

.api-status.loading .status-indicator {
  animation: pulse 1.2s ease-in-out infinite;
}

@keyframes pulse {
  0% { 
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% { 
    opacity: 1;
    transform: scale(1.1);
  }
  100% { 
    opacity: 0.3;
    transform: scale(0.8);
  }
}

.result-header h4 {
  font-size: 15px;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 5px;
}

.result {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--primary-light);
  border-radius: 8px;
  padding: 14px;
  background-color: var(--white);
  font-size: 15px;
  line-height: 1.6;
  white-space: pre-wrap;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* 底部状态栏 */
.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 10px;
  margin-top: 8px;
  border-top: 1px solid var(--primary-light);
  font-size: 12px;
  color: var(--text-light);
}

.settings-link {
  color: var(--primary-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 18px;
}

.settings-link:hover {
  color: var(--primary-dark);
}

/* 为不同阶段的API状态添加样式 */
.api-status.preparing {
  background-color: #f5f5f5;
}

.api-status.sending {
  background-color: #e8f4fd;
}

.api-status.processing {
  background-color: #e1f5fe;
}

.api-status.receiving {
  background-color: #e0f7fa;
}

.api-status.finalizing {
  background-color: #e0f2f1;
}

.api-status.completed {
  background-color: #e8f5e9;
}

.api-status.error {
  background-color: #ffebee;
}

/* 进度条样式 */
.progress-indicator {
  height: 4px;
  background: #e0e0e0;
  margin-top: 8px;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, var(--primary-light), var(--primary-dark));
  transition: width 0.3s ease-in-out;
}

/* 为preparing状态设置10%进度 */
.api-status.preparing .progress-bar {
  width: 10%;
}

/* 为sending状态设置25%进度 */
.api-status.sending .progress-bar {
  width: 25%;
}

/* 为processing状态设置50%进度 */
.api-status.processing .progress-bar {
  width: 50%;
}

/* 为receiving状态设置75%进度 */
.api-status.receiving .progress-bar {
  width: 75%;
}

/* 为finalizing状态设置90%进度 */
.api-status.finalizing .progress-bar {
  width: 90%;
}

/* 为completed状态设置100%进度 */
.api-status.completed .progress-bar {
  width: 100%;
}

/* 历史记录面板 */
.history-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 320px;
  background-color: var(--white);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--primary-light);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 16px;
  border-bottom: 1px solid var(--primary-light);
  background-color: var(--primary-bg);
}

.history-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.history-header h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.history-item {
  padding: 14px;
  border-bottom: 1px solid var(--primary-light);
  cursor: pointer;
  transition: all 0.2s;
  border-left: 3px solid transparent;
}

.history-item:hover {
  background-color: var(--primary-bg);
  border-left-color: var(--primary-color);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.history-item-model {
  font-weight: 500;
  color: #2196F3;
}

.history-item-date {
  color: #999;
}

.history-item-content {
  font-size: 13px;
  color: #333;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 5px;
}

.history-item-prompt {
  font-size: 11px;
  color: #666;
  font-style: italic;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-history {
  padding: 20px;
  text-align: center;
  color: #999;
  font-style: italic;
}

.loading-history {
  padding: 20px;
  text-align: center;
  color: #666;
}

/* 复制图标样式 */
.copy-icon {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
}

.copy-icon::before,
.copy-icon::after {
  content: '';
  position: absolute;
  border: 1.5px solid currentColor;
  border-radius: 2px;
}

.copy-icon::before {
  top: 2px;
  left: 2px;
  width: 9px;
  height: 9px;
  background-color: white;
}

.copy-icon::after {
  top: 5px;
  left: 5px;
  width: 9px;
  height: 9px;
  background-color: white;
  z-index: 1;
}

/* 历史记录图标样式 */
.history-icon {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
}

.history-icon::before {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  border: 2px solid currentColor;
  border-radius: 50%;
  top: 1px;
  left: 1px;
}

.history-icon::after {
  content: '';
  position: absolute;
  width: 7px;
  height: 2px;
  background-color: currentColor;
  transform-origin: left center;
  transform: rotate(-45deg);
  top: 9px;
  left: 9px;
}

/* 历史记录按钮悬停效果 */
.icon-btn:hover .history-icon::before,
.icon-btn:hover .history-icon::after {
  color: #007bff;
}

/* 历史记录来源标签 */
.history-source {
  font-size: 10px;
  padding: 2px 5px;
  border-radius: 3px;
  margin-left: 5px;
}

.history-source.sync {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.history-source.local {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
} 